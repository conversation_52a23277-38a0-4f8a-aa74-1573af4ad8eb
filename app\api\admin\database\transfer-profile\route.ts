import { NextRequest } from 'next/server';
import { db } from '@/db/drizzle';
import { sql } from 'drizzle-orm';

// POST transfer profile from one address to another
export async function POST(request: NextRequest): Promise<Response> {
  try {
    const { fromAddress, toAddress } = await request.json();

    if (!fromAddress || !toAddress) {
      return Response.json(
        { error: 'Both fromAddress and toAddress are required' },
        { status: 400 }
      );
    }

    if (fromAddress === toAddress) {
      return Response.json(
        { error: 'From and to addresses cannot be the same' },
        { status: 400 }
      );
    }

    // Validate addresses (basic check)
    if (typeof fromAddress !== 'string' || typeof toAddress !== 'string') {
      return Response.json(
        { error: 'Addresses must be strings' },
        { status: 400 }
      );
    }

    // Check if source profile exists
    const sourceProfile = await db.execute(sql`
      SELECT COUNT(*) as count FROM web3Profile WHERE address = ${fromAddress}
    `);

    if ((sourceProfile as any)[0].count === 0) {
      return Response.json(
        { error: 'Source profile not found' },
        { status: 404 }
      );
    }

    // Check if destination profile exists
    const destProfile = await db.execute(sql`
      SELECT COUNT(*) as count FROM web3Profile WHERE address = ${toAddress}
    `);

    let deletedRecords = 0;
    if ((destProfile as any)[0].count > 0) {
      // Delete existing destination profile - CASCADE will handle related records
      const deleteProfile = await db.execute(sql`
        DELETE FROM web3Profile WHERE address = ${toAddress}
      `);
      deletedRecords += (deleteProfile as any).affectedRows || 0;
    }

    // Copy all records from fromAddress to toAddress (much simpler than updating!)
    let totalCopied = 0;

    // Generate a new referral code for the destination profile
    const generateReferralCode = () => {
      const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
      let result = 'w3t';
      for (let i = 0; i < 5; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
      }
      return result;
    };
    const newReferralCode = generateReferralCode();

    // Copy web3Profile first (parent table) - generate new referral code to avoid duplicate
    const copyProfile = await db.execute(sql`
      INSERT INTO web3Profile (address, chain, name, theme, role, status, expiry_date, transaction_hash, referral_code, referred_by, created_at, updated_at)
      SELECT ${toAddress}, chain, name, theme, role, status, expiry_date, transaction_hash, ${newReferralCode}, referred_by, created_at, NOW()
      FROM web3Profile WHERE address = ${fromAddress}
    `);
    totalCopied += (copyProfile as any).affectedRows || 0;

    // Copy componentPositions
    const copyComponents = await db.execute(sql`
      INSERT INTO componentPositions (address, chain, component_type, \`order\`, hidden, details, created_at, updated_at)
      SELECT ${toAddress}, chain, component_type, \`order\`, hidden, details, created_at, NOW()
      FROM componentPositions WHERE address = ${fromAddress}
    `);
    totalCopied += (copyComponents as any).affectedRows || 0;

    // Copy componentImages
    const copyImages = await db.execute(sql`
      INSERT INTO componentImages (id, address, component_type, section, image_data, scale, position_x, position_y, natural_width, natural_height, created_at, updated_at)
      SELECT UUID(), ${toAddress}, component_type, section, image_data, scale, position_x, position_y, natural_width, natural_height, created_at, NOW()
      FROM componentImages WHERE address = ${fromAddress}
    `);
    totalCopied += (copyImages as any).affectedRows || 0;

    // Copy profileLikes
    const copyLikes = await db.execute(sql`
      INSERT INTO profileLikes (id, liked_address, liker_address, created_at, updated_at)
      SELECT UUID(), ${toAddress}, liker_address, created_at, NOW()
      FROM profileLikes WHERE liked_address = ${fromAddress}
    `);
    totalCopied += (copyLikes as any).affectedRows || 0;

    // Copy profileReferrals
    const copyReferrals = await db.execute(sql`
      INSERT INTO profileReferrals (id, referred_address, referrer_address, referral_code, created_at, updated_at)
      SELECT UUID(), ${toAddress}, referrer_address, referral_code, created_at, NOW()
      FROM profileReferrals WHERE referred_address = ${fromAddress}
    `);
    totalCopied += (copyReferrals as any).affectedRows || 0;

    // Now delete the old records (foreign keys will cascade properly)
    const deleteOldProfile = await db.execute(sql`
      DELETE FROM web3Profile WHERE address = ${fromAddress}
    `);
    const deletedRecordsFromSource = (deleteOldProfile as any).affectedRows || 0;

    return Response.json({
      success: true,
      message: 'Profile transferred successfully',
      totalCopied,
      deletedRecords,
      deletedRecordsFromSource,
      fromAddress,
      toAddress
    });

  } catch (error) {
    console.error('Error transferring profile:', error);
    return Response.json(
      { 
        error: 'Failed to transfer profile',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
