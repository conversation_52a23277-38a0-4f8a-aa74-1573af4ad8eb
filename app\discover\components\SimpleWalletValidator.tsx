'use client';

import { ReactNode, useState, useEffect } from 'react';
import { useAppKitAccount } from '@reown/appkit/react';
import { Loader2 } from 'lucide-react';
import AppKitButton from '@/components/AppKitButton';

interface SimpleWalletValidatorProps {
  children: ReactNode;
  fallbackContent?: ReactNode;
}

/**
 * A simplified wallet validator that only checks if the wallet is connected,
 * without checking profile status.
 */
export default function SimpleWalletValidator({
  children,
  fallbackContent
}: SimpleWalletValidatorProps) {
  const { isConnected } = useAppKitAccount();
  const [isInitializing, setIsInitializing] = useState(true);
  const [showConnectionError, setShowConnectionError] = useState(false);

  // Handle wallet initialization state
  useEffect(() => {
    // Give the wallet time to initialize and reconnect
    const timer = setTimeout(() => {
      setIsInitializing(false);
    }, 900); // Wait 0.9 seconds for wallet to initialize

    return () => clearTimeout(timer);
  }, []);

  // Handle connection error display with additional delay
  useEffect(() => {
    if (isInitializing) {
      setShowConnectionError(false);
      return;
    }

    if (!isConnected) {
      // Add extra delay before showing connection error to prevent flashing during navigation
      const errorTimer = setTimeout(() => {
        setShowConnectionError(true);
      }, 600); // Additional 0.6 second delay after initialization

      return () => clearTimeout(errorTimer);
    } else {
      setShowConnectionError(false);
    }
  }, [isInitializing, isConnected]);

  // Show loading state during initialization
  if (isInitializing) {
    return (
      <div className="bg-black/30 backdrop-blur-md rounded-xl border border-neutral-800 p-6 text-center">
        <div className="flex flex-col items-center">
          <Loader2 className="h-6 w-6 animate-spin text-blue-500 mb-2" />
          <p className="text-neutral-400 text-sm">Initializing...</p>
        </div>
      </div>
    );
  }

  // If wallet is connected, render children
  if (isConnected) {
    return <>{children}</>;
  }

  // Show connection error only after delays
  if (showConnectionError) {
    return (
    <div className="bg-black/30 backdrop-blur-md rounded-xl border border-neutral-800 p-6 text-center">
      {fallbackContent || (
        <>
          <h3 className="text-lg font-medium text-neutral-300 mb-2">Connect Your Wallet</h3>
          <p className="text-neutral-400 mb-4">
            Please connect your wallet to view and interact with profiles.
          </p>
          <div className="flex justify-center">
            <AppKitButton />
          </div>
        </>
      )}
    </div>
    );
  }

  // Show checking connection state while waiting for error delay
  return (
    <div className="bg-black/30 backdrop-blur-md rounded-xl border border-neutral-800 p-6 text-center">
      <div className="flex flex-col items-center">
        <Loader2 className="h-6 w-6 animate-spin text-blue-500 mb-2" />
        <p className="text-neutral-400 text-sm">Checking connection...</p>
      </div>
    </div>
  );
}
