/**
 * Migration script to update token holder tables for multichain support
 * This script increases the length of address and chain ID fields to support Solana addresses
 */

const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

async function runMigration() {
  let connection;
  
  try {
    // Create connection using environment variables
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'web3socials',
      port: process.env.DB_PORT || 3306
    });

    console.log('Connected to database');

    // Read the migration SQL file
    const migrationPath = path.join(__dirname, '..', 'db', 'migrations', 'update_token_holder_address_lengths.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    // Split the SQL into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    console.log(`Executing ${statements.length} migration statements...`);

    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      console.log(`Executing statement ${i + 1}/${statements.length}...`);
      console.log(statement);
      
      try {
        await connection.execute(statement);
        console.log('✓ Success');
      } catch (error) {
        console.error(`✗ Error executing statement: ${error.message}`);
        throw error;
      }
    }

    console.log('\n✅ Migration completed successfully!');
    console.log('Token holder tables have been updated to support multichain addresses.');

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('Database connection closed');
    }
  }
}

// Check if required environment variables are set
if (!process.env.DB_HOST && !process.env.DATABASE_URL) {
  console.log('⚠️  Database environment variables not found.');
  console.log('Please set the following environment variables:');
  console.log('- DB_HOST, DB_USER, DB_PASSWORD, DB_NAME, DB_PORT');
  console.log('Or set DATABASE_URL for a connection string');
  console.log('\nExample:');
  console.log('DB_HOST=localhost DB_USER=root DB_PASSWORD=yourpassword DB_NAME=web3socials node scripts/migrate-token-holder-tables.js');
  process.exit(1);
}

runMigration();
