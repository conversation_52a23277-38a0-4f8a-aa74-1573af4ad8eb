import { readFileSync } from 'fs';
import { join } from 'path';
import { db } from '@/db/drizzle';
import { web3Profile, componentPositions } from '@/db/schema';
import { eq, sql } from 'drizzle-orm';
import { saveImage, getComponentImages } from '@/lib/imageStorage';
import { getComponentDefaults, getProfileDefaults } from './systemSettings';

async function getDefaultImageAsBase64(filename: string): Promise<string> {
  const filepath = join(process.cwd(), 'public', filename);
  const buffer = readFileSync(filepath);
  return buffer.toString('base64');
}

/**
 * Check if a profile exists for the given address
 * @param address Wallet address
 * @returns Boolean indicating if profile exists
 */
export async function profileExists(address: string): Promise<boolean> {
  if (!address) return false;

  try {
    const existingProfile = await db
      .select({ count: sql`count(*)` })
      .from(web3Profile)
      .where(eq(web3Profile.address, address));

    return existingProfile.length > 0 && Number(existingProfile[0]?.count) > 0;
  } catch (error) {
    console.error('Error checking if profile exists:', error);
    return false;
  }
}

/**
 * Check if component positions exist for the given address
 * @param address Wallet address
 * @returns Boolean indicating if component positions exist
 */
export async function componentPositionsExist(address: string): Promise<boolean> {
  if (!address) return false;

  try {
    const existingPositions = await db
      .select({ count: sql`count(*)` })
      .from(componentPositions)
      .where(eq(componentPositions.address, address));

    return existingPositions.length > 0 && Number(existingPositions[0]?.count) > 0;
  } catch (error) {

    return false;
  }
}

/**
 * Create default component positions for a new user
 * @param address Wallet address
 * @param chain Blockchain network chain ID
 */
export async function createDefaultComponentPositions(address: string, chain: string = "25"): Promise<void> {
  try {
    // Creating default component positions
    const now = new Date();

    // Get component defaults from system settings
    const componentDefaults = await getComponentDefaults(chain);

    // Get default images based on component defaults
    for (const defaultComponent of componentDefaults) {
      // Only process components with default images
      if (defaultComponent.details?.defaultImagePath || (defaultComponent.details as any)?.defaultBannerImagePath || (defaultComponent.details as any)?.defaultProfileImagePath) {
        try {
          console.log(`Processing default image for ${defaultComponent.componentType}...`);
          const componentType = defaultComponent.componentType;

          // Handle special case for bannerpfp component which has two images
          if (componentType === 'bannerpfp') {
            // Process banner image
            const bannerImagePath = (defaultComponent.details as any)?.defaultBannerImagePath;
            if (bannerImagePath) {
              console.log(`Reading default ${componentType} banner image from ${bannerImagePath}...`);
              const defaultBannerImageBase64 = await getDefaultImageAsBase64(bannerImagePath);

              // Get image settings from component defaults
              const bannerImageSettings = {
                scale: "1",  // Default scale
                positionX: 0, // Default X position
                positionY: 0  // Default Y position
              };

              console.log(`Saving ${componentType} banner image to componentImages table with settings:`, bannerImageSettings);
              const bannerImageId = await saveImage(address, componentType, 'banner', defaultBannerImageBase64, bannerImageSettings);
              console.log(`Saved ${componentType} banner image to componentImages table with ID: ${bannerImageId}`);

              // Verify the image was saved
              const bannerImages = await getComponentImages(address, componentType, 'banner');
              console.log(`Verified ${componentType} banner images: ${bannerImages.length} found`);
            }

            // Process profile image
            const profileImagePath = (defaultComponent.details as any)?.defaultProfileImagePath;
            if (profileImagePath) {
              console.log(`Reading default ${componentType} profile image from ${profileImagePath}...`);
              const defaultProfileImageBase64 = await getDefaultImageAsBase64(profileImagePath);

              // Get image settings from component defaults
              const profileImageSettings = {
                scale: "1",  // Default scale
                positionX: 0, // Default X position
                positionY: 0  // Default Y position
              };

              console.log(`Saving ${componentType} profile image to componentImages table with settings:`, profileImageSettings);
              const profileImageId = await saveImage(address, componentType, 'profile', defaultProfileImageBase64, profileImageSettings);
              console.log(`Saved ${componentType} profile image to componentImages table with ID: ${profileImageId}`);

              // Verify the image was saved
              const profileImages = await getComponentImages(address, componentType, 'profile');
              console.log(`Verified ${componentType} profile images: ${profileImages.length} found`);
            }

            // Skip the rest of the loop for bannerpfp component
            continue;
          }

          // For other components with a single image
          const imagePath = defaultComponent.details?.defaultImagePath;

          if (!imagePath) {
            console.log(`No default image path found for ${componentType}`);
            continue;
          }

          console.log(`Reading default ${componentType} image from ${imagePath}...`);
          const defaultImageBase64 = await getDefaultImageAsBase64(imagePath);

          // Get image settings from component defaults
          const imageSettings = {
            scale: "1",  // Default scale
            positionX: 0, // Default X position
            positionY: 0  // Default Y position
          };

          console.log(`Saving ${componentType} image to componentImages table with settings:`, imageSettings);
          const imageId = await saveImage(address, componentType, '0', defaultImageBase64, imageSettings);
          console.log(`Saved ${componentType} image to componentImages table with ID: ${imageId}`);

          // Verify the image was saved
          const images = await getComponentImages(address, componentType);
          console.log(`Verified ${componentType} images: ${images.length} found`);
        } catch (error) {
          console.error(`Error saving ${defaultComponent.componentType} image to componentImages table:`, error);
          // Continue with other components even if one fails
        }
      }
    }

    // Insert components based on database defaults
    for (const defaultComponent of componentDefaults) {
      const component: any = {
        address,
        chain,
        componentType: defaultComponent.componentType,
        order: defaultComponent.order,
        hidden: defaultComponent.hidden,
        createdAt: now,
        updatedAt: now
      };

      // Add details based on component type
      if (defaultComponent.componentType === 'banner') {
        component.details = {
          backgroundColor: defaultComponent.details?.backgroundColor || 'transparent',
          fontColor: defaultComponent.details?.fontColor || null,
          scale: (defaultComponent.details as any)?.scale || "1",
          positionX: (defaultComponent.details as any)?.positionX || 0,
          positionY: (defaultComponent.details as any)?.positionY || 0,
          naturalWidth: (defaultComponent.details as any)?.naturalWidth || null,
          naturalHeight: (defaultComponent.details as any)?.naturalHeight || null
        };
      // profilePicture component is deprecated, use bannerpfp instead
      } else if (defaultComponent.componentType === 'hero') {
        // Use heroContent from system settings if available, otherwise use default
        const defaultHeroContent = defaultComponent.details?.heroContent || [
          {
            title: "My First Section",
            description: "This is my first section. Click edit to change this text.",
            contentType: 'color',
            colorGradient: 'linear-gradient(to bottom right, var(--cyan-500), var(--emerald-500))'
          }
        ];

        component.details = {
          backgroundColor: defaultComponent.details?.backgroundColor || 'transparent',
          fontColor: defaultComponent.details?.fontColor || null,
          heroContent: defaultHeroContent
        };
      } else if (defaultComponent.componentType === 'bannerpfp') {
        // Get profile defaults for name and bio
        const profileDefaults = await getProfileDefaults(chain);
        const defaultNameFormat = profileDefaults.default_profile_name_format || 'Web3 User {address}';
        const formattedName = defaultNameFormat.replace('{address}', address.substring(0, 8));
        const defaultBio = profileDefaults.default_profile_bio || '';

        // Use profileNameStyle from system settings if available, otherwise use default
        const defaultProfileNameStyle = defaultComponent.details?.profileNameStyle || {
          fontSize: '1.5rem',
          fontWeight: 'bold',
          fontColor: '#ffffff',
          effect: 'typewriter'
        };

        component.details = {
          backgroundColor: defaultComponent.details?.backgroundColor || 'transparent',
          fontColor: defaultComponent.details?.fontColor || null,
          profileShape: defaultComponent.details?.profileShape || 'circular',
          profileHorizontalPosition: defaultComponent.details?.profileHorizontalPosition || 50,
          profileNameHorizontalPosition: defaultComponent.details?.profileNameHorizontalPosition || 50,
          profileNameStyle: defaultProfileNameStyle,
          profileName: defaultComponent.details?.profileName || formattedName,
          profileBio: defaultComponent.details?.profileBio || defaultBio
        };
      } else if (defaultComponent.componentType === 'socialLinks') {
        // Use socialLinks from system settings if available, otherwise use empty defaults
        const defaultSocialLinks = defaultComponent.details?.socialLinks || {
          twitter: '',
          discord: '',
          telegram: '',
          website: '',
          facebook: '',
          youtube: '',
          email: '',
          linkedin: '',
          cro: ''
        };

        component.details = {
          backgroundColor: defaultComponent.details?.backgroundColor || 'transparent',
          fontColor: defaultComponent.details?.fontColor || null,
          socialLinks: defaultSocialLinks
        };
      }

      // Insert the component
      await db.insert(componentPositions).values(component);
    }

    console.log(`Created default component positions and uploaded default images for address: ${address}`);
  } catch (error) {
    console.error('Error creating default component positions:', error);
    throw error;
  }
}

/**
 * Create default profile for a new user
 * @param address Wallet address
 * @param chain Blockchain network chain ID
 */
export async function createDefaultProfile(address: string, chain: string = "25"): Promise<void> {
  if (!address) return;

  try {
    console.log(`Creating default profile for address: ${address}`);

    // Get profile defaults from system settings
    const profileDefaults = await getProfileDefaults(chain);

    // Calculate expiry date if default_expiry_days is set
    let expiryDate = null;
    if (profileDefaults.default_expiry_days) {
      const days = parseInt(profileDefaults.default_expiry_days);
      if (!isNaN(days) && days > 0) {
        expiryDate = new Date();
        expiryDate.setDate(expiryDate.getDate() + days);
      }
    }

    // Create profile with values from database defaults
    await db.insert(web3Profile).values({
      address,
      chain,
      role: profileDefaults.default_role,
      status: profileDefaults.default_status,
      expiryDate,
      transactionHash: null,
    });

    console.log(`Created default profile for address: ${address}`);
  } catch (error) {
    console.error('Error creating default profile:', error);
    throw error;
  }
}

/**
 * Ensure default profile and component positions exist for a user
 * @param address Wallet address
 * @param chain Blockchain network chain ID
 */
export async function ensureDefaults(address: string, chain: string = "25"): Promise<void> {
  if (!address) return;

  try {

    // Check if profile exists
    const profileExistsResult = await profileExists(address);

    // If profile doesn't exist, create it
    if (!profileExistsResult) {
      await createDefaultProfile(address, chain);
    }

    // Check if component positions exist
    const positionsExist = await componentPositionsExist(address);

    // If component positions don't exist, create them
    if (!positionsExist) {
      await createDefaultComponentPositions(address, chain);
    }
  } catch (error) {
    console.error('Error ensuring defaults:', error);
    throw error;
  }
}

/**
 * Get profile data with components for the given address
 * If profile doesn't exist, create default profile and components
 * @param address Wallet address
 * @param chain Blockchain network chain ID
 * @returns Profile data with components
 */
export async function getOrCreateProfile(address: string, chain: string = "25"): Promise<any> {
  if (!address) throw new Error('Address is required');

  try {
    console.log(`Getting or creating profile for address: ${address}`);

    // Ensure defaults exist
    await ensureDefaults(address, chain);

    // Get profile data
    const profile = await db
      .select()
      .from(web3Profile)
      .where(eq(web3Profile.address, address));

    if (profile.length === 0) {
      throw new Error('Failed to create or retrieve profile');
    }

    // Get component positions
    const components = await db
      .select()
      .from(componentPositions)
      .where(eq(componentPositions.address, address))
      .orderBy(componentPositions.order);

    console.log(`Found ${components.length} components for address: ${address}`);

    // Return profile data with components
    return {
      ...profile[0],
      components: components.map(c => ({
        componentType: c.componentType,
        order: c.order,
        hidden: c.hidden
      })),
    };
  } catch (error) {
    console.error('Error getting or creating profile:', error);
    throw error;
  }
}
