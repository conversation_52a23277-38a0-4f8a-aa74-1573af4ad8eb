import { NextRequest } from 'next/server';
import { getTokenRequirementsF<PERSON><PERSON>hain, getTokenRequirements } from '@/app/utils/systemSettings';

type Context = {
  params: Promise<{
    chainId: string;
  }>;
};

export async function GET(
  request: NextRequest,
  context: Context
): Promise<Response> {
  try {
    const { chainId } = await context.params;
    const { searchParams } = new URL(request.url);
    const full = searchParams.get('full') === 'true';

    if (!chainId) {
      return Response.json(
        { error: 'Chain ID is required' },
        { status: 400 }
      );
    }

    // Get full token requirements structure if requested
    if (full) {
      const fullRequirements = await getTokenRequirements(chainId);
      return Response.json(fullRequirements, {
        headers: {
          'Cache-Control': 'public, max-age=5, stale-while-revalidate=10',
        }
      });
    }

    // Get legacy token requirements for backward compatibility
    const requirements = await getTokenRequirementsForChain(chainId);

    return Response.json(requirements, {
      headers: {
        'Cache-Control': 'public, max-age=5, stale-while-revalidate=10',
      }
    });
  } catch (error) {
    return Response.json(
      { error: 'Failed to fetch token requirements' },
      { status: 500 }
    );
  }
}
