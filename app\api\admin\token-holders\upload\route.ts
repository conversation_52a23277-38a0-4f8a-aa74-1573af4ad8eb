import { NextRequest } from 'next/server';
import { 
  parseTokenHolderCSV, 
  createTokenHolderSnapshot, 
  createTokenHolderJob, 
  updateTokenHolderJob 
} from '@/lib/tokenHolderUtils';

export async function POST(request: NextRequest): Promise<Response> {
  let jobId: number | null = null;
  
  try {
    const formData = await request.formData();
    const file = formData.get('csvFile') as File;
    const contractAddress = formData.get('contractAddress') as string;
    const chainId = formData.get('chainId') as string;
    const tokenName = formData.get('tokenName') as string;

    if (!file || !contractAddress || !chainId) {
      return Response.json(
        { error: 'CSV file, contract address, and chain ID are required' },
        { status: 400 }
      );
    }

    // Validate file type
    if (!file.name.endsWith('.csv') && file.type !== 'text/csv') {
      return Response.json(
        { error: 'Please upload a CSV file' },
        { status: 400 }
      );
    }

    // Create job tracking record
    jobId = await createTokenHolderJob(contractAddress, chainId, tokenName);
    await updateTokenHolderJob(jobId, 'processing');

    // Read CSV content
    const csvContent = await file.text();

    if (!csvContent) {
      throw new Error('CSV file is empty');
    }

    console.log(`Processing uploaded CSV file: ${file.name} (${file.size} bytes)`);

    // Debug: Log first few lines of CSV
    const lines = csvContent.trim().split('\n');
    console.log('CSV Debug - First 5 lines:');
    lines.slice(0, 5).forEach((line, i) => {
      console.log(`  Line ${i}: ${line}`);
    });

    // Basic validation that this looks like CSV data
    const hasCommas = csvContent.includes(',');
    const hasAddressColumn = csvContent.toLowerCase().includes('address') ||
                            csvContent.toLowerCase().includes('holder') ||
                            csvContent.toLowerCase().includes('account');

    if (!hasCommas || !hasAddressColumn) {
      throw new Error('CSV file does not appear to contain valid token holder data. Expected columns: Address/Holder, Balance, and optionally PendingBalanceUpdate');
    }

    // Parse the CSV content with chain-specific handling
    const holders = parseTokenHolderCSV(csvContent, chainId);

    if (holders.length === 0) {
      const addressFormatMsg = chainId === 'sol1' || chainId?.includes('sol')
        ? 'Solana addresses (base58 format, 32-44 characters)'
        : 'EVM-compatible addresses (0x format, 42 characters)';
      throw new Error(`No valid token holders found in the CSV file. Please check the format and ensure addresses are valid ${addressFormatMsg}. Supported formats: EVM chains (0x...), Solana (base58), and other standard blockchain address formats.`);
    }



    // Create snapshot in database
    const snapshotId = await createTokenHolderSnapshot(
      contractAddress,
      chainId,
      tokenName || `Token ${contractAddress.slice(0, 8)}...`,
      holders
    );

    // Update job as completed
    await updateTokenHolderJob(jobId, 'completed', holders.length);

    return Response.json({
      success: true,
      snapshotId,
      totalHolders: holders.length,
      contractAddress,
      chainId,
      tokenName,
      message: `Successfully processed ${holders.length} token holders from uploaded CSV`
    });

  } catch (error) {

    
    // Update job as failed if we have a job ID
    if (jobId) {
      await updateTokenHolderJob(
        jobId, 
        'failed', 
        undefined, 
        error instanceof Error ? error.message : 'Unknown error'
      );
    }

    return Response.json(
      { 
        error: 'Failed to process uploaded CSV',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
