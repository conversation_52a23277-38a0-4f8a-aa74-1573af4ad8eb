import { NextRequest } from 'next/server';
import { db } from '@/db/drizzle';
import { sql } from 'drizzle-orm';

// POST execute SQL query
export async function POST(request: NextRequest): Promise<Response> {
  try {
    const { query } = await request.json();

    if (!query || typeof query !== 'string') {
      return Response.json(
        { error: 'Query is required and must be a string' },
        { status: 400 }
      );
    }

    // Basic security checks
    const queryLower = query.toLowerCase().trim();
    
    // Block dangerous operations
    const dangerousOperations = [
      'drop table',
      'drop database',
      'truncate',
      'alter table',
      'create table',
      'create database',
      'grant',
      'revoke',
      'flush',
      'shutdown',
      'restart'
    ];

    const isDangerous = dangerousOperations.some(op => 
      queryLower.includes(op)
    );

    if (isDangerous) {
      return Response.json(
        { error: 'Dangerous operations are not allowed' },
        { status: 403 }
      );
    }

    // Limit query length
    if (query.length > 10000) {
      return Response.json(
        { error: 'Query is too long (max 10000 characters)' },
        { status: 400 }
      );
    }

    // Execute the query
    const startTime = Date.now();
    const result = await db.execute(sql.raw(query));
    const executionTime = Date.now() - startTime;

    // Format the response - handle MySQL result structure
    let formattedResult;

    // Check if this is a SELECT query by looking at the query text
    const isSelectQuery = queryLower.trim().startsWith('select') ||
                         queryLower.trim().startsWith('show') ||
                         queryLower.trim().startsWith('describe') ||
                         queryLower.trim().startsWith('explain');

    if (isSelectQuery) {
      // For SELECT queries, extract only the data rows (filter out metadata)
      let rows: any[] = [];

      if (Array.isArray(result)) {
        // Filter out MySQL metadata objects - keep only plain data objects
        rows = result.filter((item: any) => {
          // MySQL metadata objects have properties like _buf, _clientEncoding, etc.
          // Data objects should only have the column names as properties
          return item &&
                 typeof item === 'object' &&
                 !('_buf' in item) &&
                 !('_clientEncoding' in item) &&
                 !('characterSet' in item) &&
                 !('columnType' in item);
        });
      }

      formattedResult = {
        type: 'select',
        rows: rows,
        rowCount: rows.length,
        executionTime: `${executionTime}ms`
      };
    } else {
      // For non-SELECT queries (INSERT, UPDATE, DELETE, etc.)
      formattedResult = {
        type: 'modification',
        result: result,
        executionTime: `${executionTime}ms`
      };
    }

    return Response.json(formattedResult, {
      headers: {
        'Cache-Control': 'no-store, no-cache, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
  } catch (error) {
    console.error('Error executing SQL query:', error);
    return Response.json(
      { 
        error: 'Failed to execute query',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
