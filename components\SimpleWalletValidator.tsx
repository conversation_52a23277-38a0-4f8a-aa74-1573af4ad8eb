'use client';

import { ReactNode, useState, useEffect } from 'react';
import { useAppKitAccount } from '@reown/appkit/react';
import { Loader2 } from 'lucide-react';
import Image from 'next/image';

interface SimpleWalletValidatorProps {
  children: ReactNode;
  fallbackContent?: ReactNode;
}

// A simplified version of WalletValidator that only checks if the wallet is connected
// without checking the profile status
export default function SimpleWalletValidator({
  children,
  fallbackContent
}: SimpleWalletValidatorProps) {
  const { isConnected } = useAppKitAccount();
  const [isInitializing, setIsInitializing] = useState(true);
  const [showConnectionError, setShowConnectionError] = useState(false);

  // Handle wallet initialization state
  useEffect(() => {
    // Give the wallet time to initialize and reconnect
    const timer = setTimeout(() => {
      setIsInitializing(false);
    }, 900); // Wait 0.9 seconds for wallet to initialize

    return () => clearTimeout(timer);
  }, []);

  // Handle connection error display with additional delay
  useEffect(() => {
    if (isInitializing) {
      setShowConnectionError(false);
      return;
    }

    if (!isConnected) {
      // Add extra delay before showing connection error to prevent flashing during navigation
      const errorTimer = setTimeout(() => {
        setShowConnectionError(true);
      }, 600); // Additional 0.6 second delay after initialization

      return () => clearTimeout(errorTimer);
    } else {
      setShowConnectionError(false);
    }
  }, [isInitializing, isConnected]);

  // Empty fallback content
  const emptyFallbackContent = (
    <div className="mt-8 max-w-md mx-auto">
      <h2 className="text-lg font-semibold mb-4">Wallet Connection Required</h2>
      <div className="bg-yellow-100 dark:bg-yellow-900/30 border border-yellow-300 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200 rounded-md p-4 mb-4">
        Please connect your wallet to continue.
      </div>
    </div>
  );

  return (
    <>
      {isInitializing ? (
        // Show loading state during wallet initialization
        <div className="flex flex-col items-center justify-center min-h-[200px] p-6">
          <Loader2 className="h-6 w-6 animate-spin text-blue-500 mb-2" />
          <p className="text-neutral-400 text-sm">Initializing...</p>
        </div>
      ) : isConnected ? (
        children
      ) : showConnectionError ? (
        fallbackContent || emptyFallbackContent
      ) : (
        // Show nothing while waiting for connection check delay
        <div className="flex flex-col items-center justify-center min-h-[200px] p-6">
          <Loader2 className="h-6 w-6 animate-spin text-blue-500 mb-2" />
          <p className="text-neutral-400 text-sm">Checking connection...</p>
        </div>
      )}
    </>
  );
}
