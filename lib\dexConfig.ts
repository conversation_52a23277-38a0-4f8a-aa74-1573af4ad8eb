/**
 * DEX Router configurations for different chains
 * Used by the snipper auto-buy functionality
 */

import { cronos, mainnet, polygon, bsc, arbitrum } from 'viem/chains';

export interface DexConfig {
  chain: any;
  rpcUrl: string;
  dexRouter: string;
  wrappedNative: string;
  gasPrice: string;
  dexName: string;
  explorerUrl: string;
}

export const DEX_CONFIGS: Record<string, DexConfig> = {
  '25': {
    chain: cronos,
    rpcUrl: 'https://evm.cronos.org',
    dexRouter: '******************************************', // VVS Finance Router
    wrappedNative: '******************************************', // WCRO
    gasPrice: '5000000000000', // 5000 gwei for Cronos
    dexName: 'VVS Finance',
    explorerUrl: 'https://cronoscan.com'
  },
  '1': {
    chain: mainnet,
    rpcUrl: 'https://eth.llamarpc.com',
    dexRouter: '******************************************', // Uniswap V2 Router
    wrappedNative: '******************************************', // WETH
    gasPrice: '20000000000', // 20 gwei
    dexName: 'Uniswap V2',
    explorerUrl: 'https://etherscan.io'
  },
  '137': {
    chain: polygon,
    rpcUrl: 'https://polygon.llamarpc.com',
    dexRouter: '******************************************', // QuickSwap Router
    wrappedNative: '******************************************', // WMATIC
    gasPrice: '30000000000', // 30 gwei
    dexName: 'QuickSwap',
    explorerUrl: 'https://polygonscan.com'
  },
  '56': {
    chain: bsc,
    rpcUrl: 'https://bsc.llamarpc.com',
    dexRouter: '******************************************', // PancakeSwap Router
    wrappedNative: '******************************************', // WBNB
    gasPrice: '5000000000', // 5 gwei
    dexName: 'PancakeSwap',
    explorerUrl: 'https://bscscan.com'
  },
  '42161': {
    chain: arbitrum,
    rpcUrl: 'https://arbitrum.llamarpc.com',
    dexRouter: '******************************************', // SushiSwap Router
    wrappedNative: '******************************************', // WETH
    gasPrice: '100000000', // 0.1 gwei
    dexName: 'SushiSwap',
    explorerUrl: 'https://arbiscan.io'
  }
};

/**
 * Get DEX configuration for a specific chain
 */
export function getDexConfig(chainId: string): DexConfig | null {
  return DEX_CONFIGS[chainId] || null;
}

/**
 * Get all supported chain IDs
 */
export function getSupportedChains(): string[] {
  return Object.keys(DEX_CONFIGS);
}

/**
 * Check if a chain is supported for auto-buy
 */
export function isChainSupported(chainId: string): boolean {
  return chainId in DEX_CONFIGS;
}

/**
 * Get native token symbol for a chain
 */
export function getNativeTokenSymbol(chainId: string): string {
  const symbols: Record<string, string> = {
    '25': 'CRO',
    '1': 'ETH',
    '137': 'MATIC',
    '56': 'BNB',
    '42161': 'ETH'
  };
  return symbols[chainId] || 'ETH';
}

/**
 * Format explorer URL for a transaction
 */
export function getExplorerTxUrl(chainId: string, txHash: string): string {
  const config = getDexConfig(chainId);
  if (!config) return '';
  return `${config.explorerUrl}/tx/${txHash}`;
}

/**
 * Format explorer URL for a token
 */
export function getExplorerTokenUrl(chainId: string, tokenAddress: string): string {
  const config = getDexConfig(chainId);
  if (!config) return '';
  return `${config.explorerUrl}/token/${tokenAddress}`;
}
