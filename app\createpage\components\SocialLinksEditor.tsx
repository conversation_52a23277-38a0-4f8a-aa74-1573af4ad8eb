'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Plus, Trash } from 'lucide-react';

interface SocialLink {
  platform: string;
  url: string;
  customLabel?: string;
}

interface SocialLinksEditorProps {
  initialLinks?: Record<string, string>;
  onChange: (links: Record<string, string>) => void;
  disabled?: boolean;
  backgroundColor?: string;
  fontColor?: string;
}

export default function SocialLinksEditor({ initialLinks = {}, onChange, disabled = false, backgroundColor = 'transparent', fontColor }: SocialLinksEditorProps) {
  // State for social links
  const [links, setLinks] = useState<SocialLink[]>([]);

  // Initialize links from initialLinks prop
  useEffect(() => {
    // Only run this effect once on mount
    if (links.length === 0) {
      // Convert initialLinks object to array format
      const convertedLinks = Object.entries(initialLinks)
        .filter(([_, url]) => url) // Filter out empty URLs
        .map(([platform, url]) => {
          // Check if this is a custom link with embedded label
          const urlStr = String(url);
          let customLabel = undefined;
          let finalUrl = urlStr;

          if (platform.startsWith('custom') && urlStr.includes('|')) {
            const parts = urlStr.split('|');
            if (parts.length >= 2) {
              customLabel = parts[0].trim();
              finalUrl = parts.slice(1).join('|').trim();
            }
          }

          return {
            platform,
            url: finalUrl,
            customLabel
          };
        });

      setLinks(convertedLinks);
    }
  }, [initialLinks]); // Only depend on initialLinks

  // Available platforms for the dropdown
  const [availablePlatforms, setAvailablePlatforms] = useState([
    { id: 'twitter', label: 'Twitter', icon: '/twitter.gif' },
    { id: 'discord', label: 'Discord', icon: '/discord.gif' },
    { id: 'telegram', label: 'Telegram', icon: '/tg.gif' },
    { id: 'website', label: 'Website', icon: '/www.gif' },
    { id: 'facebook', label: 'Facebook', icon: '/fb.gif' },
    { id: 'youtube', label: 'YouTube', icon: '/yt.gif' },
    { id: 'email', label: 'Email', icon: '/email.gif' },
    { id: 'linkedin', label: 'LinkedIn', icon: '/link.gif' },
    { id: 'cro', label: 'CRO Referral', icon: '/cro.gif' },
    { id: 'custom', label: 'Custom', icon: '/link.gif' },
  ]);

  // Update parent component when links change
  useEffect(() => {
    // Skip the initial render to prevent infinite loops
    const linksObject = links.reduce((acc, link) => {
      // For custom links, include the custom label in the URL value
      if (link.platform.startsWith('custom') && link.customLabel) {
        acc[link.platform] = `${link.customLabel}|${link.url}`;
      } else {
        acc[link.platform] = link.url;
      }
      return acc;
    }, {} as Record<string, string>);

    // Use JSON.stringify to compare current links with initialLinks
    const currentLinksStr = JSON.stringify(linksObject);
    const initialLinksStr = JSON.stringify(initialLinks);

    // Only call onChange if the links have actually changed
    if (currentLinksStr !== initialLinksStr) {
      onChange(linksObject);
    }
  }, [links, initialLinks, onChange]);

  // Get available platforms excluding those already used
  const getFilteredPlatforms = () => {
    const usedPlatforms = links.map(link => link.platform);
    return availablePlatforms.filter(platform =>
      platform.id === 'custom' || !usedPlatforms.includes(platform.id)
    );
  };

  // Add a new social link
  const addSocialLink = (platformId: string) => {
    const platformDefaults = getPlatformDefaults(platformId);

    if (platformId === 'custom') {
      // Add a custom platform with default URL
      setLinks([...links, {
        platform: `custom${links.length}`,
        url: platformDefaults.defaultUrl,
        customLabel: 'Custom Link'
      }]);
    } else {
      // Add a predefined platform with default URL
      const platform = availablePlatforms.find(p => p.id === platformId);
      if (platform) {
        setLinks([...links, {
          platform: platform.id,
          url: platformDefaults.defaultUrl
        }]);
      }
    }
  };

  // Remove a social link
  const removeSocialLink = (index: number) => {
    const newLinks = [...links];
    newLinks.splice(index, 1);
    setLinks(newLinks);
  };

  // Update a social link
  const updateSocialLink = (index: number, field: keyof SocialLink, value: string) => {
    const newLinks = [...links];
    newLinks[index] = { ...newLinks[index], [field]: value };
    setLinks(newLinks);
  };

  // Get platform label and icon
  const getPlatformInfo = (platform: string) => {
    // Check if it's a custom platform
    if (platform.startsWith('custom')) {
      return { label: links.find(l => l.platform === platform)?.customLabel || 'Custom Link', icon: '/link.gif' };
    }

    // Find in available platforms
    const platformInfo = availablePlatforms.find(p => p.id === platform);
    return platformInfo || { label: platform, icon: '/link.gif' };
  };

  // Get platform-specific default URL and placeholder
  const getPlatformDefaults = (platform: string) => {
    const defaults: Record<string, { defaultUrl: string; placeholder: string }> = {
      twitter: { defaultUrl: 'https://x.com/', placeholder: 'yourusername' },
      discord: { defaultUrl: 'https://discord.gg/', placeholder: 'yourinvite' },
      telegram: { defaultUrl: 'https://t.me/', placeholder: 'yourusername' },
      website: { defaultUrl: 'https://', placeholder: 'yourwebsite.com' },
      facebook: { defaultUrl: 'https://facebook.com/', placeholder: 'yourpage' },
      youtube: { defaultUrl: 'https://youtube.com/@', placeholder: 'yourchannel' },
      email: { defaultUrl: '', placeholder: '<EMAIL>' },
      linkedin: { defaultUrl: 'https://linkedin.com/in/', placeholder: 'yourprofile' },
      cro: { defaultUrl: 'https://crypto.com/app/', placeholder: 'w3t12345' },
      custom: { defaultUrl: 'https://', placeholder: 'yourlink.com' }
    };

    return defaults[platform] || { defaultUrl: '', placeholder: 'Enter your URL' };
  };

  return (
    <div className="space-y-6">
      {/* Part 1: Selected social links */}
      <div className="p-4 rounded-md space-y-4" style={{ backgroundColor }}>
        {links.map((link, index) => {
          const platformInfo = getPlatformInfo(link.platform);

          return (
            <div key={index} className="flex items-center space-x-4">
              <div className="w-12 h-12 flex-shrink-0 flex items-center justify-center">
                <Image
                  src={platformInfo.icon}
                  alt={platformInfo.label}
                  width={48}
                  height={48}
                  className="rounded-full"
                />
              </div>

              <div className="flex-grow space-y-2">
                {link.platform.startsWith('custom') && (
                  <Input
                    value={link.customLabel || ''}
                    onChange={(e) => updateSocialLink(index, 'customLabel', e.target.value)}
                    placeholder="Custom platform name"
                    className="bg-neutral-800/50 border-neutral-700 mb-2"
                    disabled={disabled}
                    style={{ color: fontColor || undefined }}
                  />
                )}

                <Input
                  value={link.url}
                  onChange={(e) => updateSocialLink(index, 'url', e.target.value)}
                  placeholder={getPlatformDefaults(link.platform).placeholder}
                  className="bg-neutral-800/50 border-neutral-700"
                  disabled={disabled}
                  style={{ color: fontColor || undefined }}
                />
              </div>

              <Button
                type="button"
                variant="ghost"
                size="icon"
                onClick={() => removeSocialLink(index)}
                disabled={disabled}
                className="text-red-500 hover:text-red-400 hover:bg-red-500/10"
              >
                <Trash className="h-5 w-5" />
              </Button>
            </div>
          );
        })}
      </div>

      {/* Part 2: Available social links */}
      {getFilteredPlatforms().length > 0 && (
        <div className="p-4 rounded-md">
          <div className="text-sm text-neutral-400 mb-2">Add social link:</div>
          <div className="flex flex-wrap gap-2">
            {getFilteredPlatforms().map((platform) => (
              <Button
                key={platform.id}
                type="button"
                variant="outline"
                size="sm"
                onClick={() => addSocialLink(platform.id)}
                disabled={disabled}
                className="flex items-center space-x-2 bg-neutral-800/50 border-neutral-700 hover:bg-neutral-700"
              >
                <Image
                  src={platform.icon}
                  alt={platform.label}
                  width={20}
                  height={20}
                  className="rounded-full"
                />
                <span>{platform.label}</span>
                <Plus className="h-4 w-4" />
              </Button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
