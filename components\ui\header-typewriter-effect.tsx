"use client";

import { cn } from "@/lib/utils";
import { motion } from "motion/react";

export const HeaderTypewriterEffect = ({
    words,
    className,
    cursorClassName,
    style,
}: {
    words: {
        text: string;
        className?: string;
    }[];
    className?: string;
    cursorClassName?: string;
    style?: React.CSSProperties;
}) => {
    // Extract the color from style for direct application
    const textColor = style?.color || 'inherit';
    // split text inside of words into array of characters
    const wordsArray = words.map((word) => {
        return {
            ...word,
            text: word.text.split(""),
        };
    });

    const renderWords = () => {
        return (
            <div className="text-center">
                {wordsArray.map((word, idx) => {
                    return (
                        <div key={`word-${idx}`} className="inline-block">
                            {word.text.map((char, index) => (
                                <span
                                    key={`char-${index}`}
                                    className=""
                                    style={{
                                        ...style,
                                        color: textColor
                                    }}
                                >
                                    {char}
                                </span>
                            ))}
                            &nbsp;
                        </div>
                    );
                })}
            </div>
        );
    };

    return (
        <div className={cn("flex space-x-1", className)}>
            <motion.div
                className="overflow-hidden"
                initial={{
                    width: "0%",
                }}
                whileInView={{
                    width: "fit-content",
                }}
                transition={{
                    duration: 2,
                    ease: "linear",
                    delay: 1,
                }}
            >
                <div
                    className="font-bold"
                    style={{
                        whiteSpace: "nowrap",
                        color: textColor,
                        fontSize: style?.fontSize || '1.5rem',
                        ...style // Apply any additional styles passed in
                    }}
                >
                    {renderWords()}{" "}
                </div>{" "}
            </motion.div>
            <motion.span
                initial={{
                    opacity: 0,
                }}
                animate={{
                    opacity: 1,
                }}
                transition={{
                    duration: 0.8,
                    repeat: Infinity,
                    repeatType: "reverse",
                }}
                className={cn(
                    "block rounded-sm w-[3px] h-8 bg-blue-500", // Fixed height for all screen sizes
                    cursorClassName
                )}
            ></motion.span>
        </div>
    );
};
