import { db } from '@/db/drizzle';
import { web3Profile, componentPositions } from '@/db/schema';
import { eq, sql } from 'drizzle-orm';
import { getComponentDefaults, getProfileDefaults } from './systemSettings';
import { getComponentImages } from '@/lib/imageStorage';

/**
 * Check if a profile exists for the given address
 * @param address Wallet address
 * @returns Boolean indicating if profile exists
 */
export async function profileExists(address: string): Promise<boolean> {
  if (!address) return false;

  try {
    const existingProfile = await db
      .select({ count: sql`count(*)` })
      .from(web3Profile)
      .where(eq(web3Profile.address, address));

    return existingProfile.length > 0 && Number(existingProfile[0]?.count) > 0;
  } catch (error) {
    return false;
  }
}

/**
 * Check if component positions exist for the given address
 * @param address Wallet address
 * @returns Boolean indicating if component positions exist
 */
export async function componentPositionsExist(address: string): Promise<boolean> {
  if (!address) return false;

  try {
    const existingPositions = await db
      .select({ count: sql`count(*)` })
      .from(componentPositions)
      .where(eq(componentPositions.address, address));

    return existingPositions.length > 0 && Number(existingPositions[0]?.count) > 0;
  } catch (error) {

    return false;
  }
}

/**
 * Create default component positions for a new user
 * @param address Wallet address
 * @param chain Blockchain network chain ID
 */
export async function createDefaultComponentPositions(address: string, chain: string = "25"): Promise<void> {
  if (!address) return;

  try {
    console.log(`Creating default component positions for address: ${address}`);

    // Get component defaults from system settings
    const componentDefaults = await getComponentDefaults(chain);

    // Get profile defaults for name and bio
    const profileDefaults = await getProfileDefaults(chain);

    // Format the default profile name using the template from settings
    const defaultNameFormat = profileDefaults.default_profile_name_format;
    const formattedName = defaultNameFormat.replace('{address}', address.substring(0, 8));

    // Get default bio from settings
    const defaultBio = profileDefaults.default_profile_bio;

    // Insert components based on database defaults
    for (const defaultComponent of componentDefaults) {
      // Create component with default values
      const component: any = {
        address,
        chain,
        componentType: defaultComponent.componentType,
        order: defaultComponent.order,
        hidden: defaultComponent.hidden,
        details: {
          backgroundColor: 'transparent',
          fontColor: null
        }
      };

      // Add component-specific details based on component type
      if (defaultComponent.componentType === 'banner') {
        // Banner doesn't need any additional details beyond the common ones
      } else if (defaultComponent.componentType === 'profilePicture') {
        component.details = {
          ...component.details,
          shape: 'circular',
          compPosition: undefined, // No default position for profile picture
          profileName: formattedName, // Use formatted name from system settings
          profileBio: defaultBio // Use default bio from system settings
        };
      } else if (defaultComponent.componentType === 'hero') {
        component.details = {
          ...component.details,
          heroContent: [
            {
              title: "My First Section",
              description: "This is my first section. Click edit to change this text.",
              contentType: 'color',
              colorGradient: 'linear-gradient(to bottom right, var(--cyan-500), var(--emerald-500))'
            }
          ]
        };
      } else if (defaultComponent.componentType === 'bannerpfp') {
        component.details = {
          ...component.details,
          profileShape: 'circular',
          profileHorizontalPosition: 50,
          profileName: formattedName, // Use formatted name from system settings
          profileBio: defaultBio // Use default bio from system settings
        };
      } else if (defaultComponent.componentType === 'socialLinks') {
        component.details = {
          ...component.details,
          socialLinks: {
            twitter: '',
            discord: '',
            telegram: '',
            website: '',
            facebook: '',
            youtube: '',
            email: '',
            linkedin: '',
            cro: ''
          }
        };
      }

      // Insert the component
      await db.insert(componentPositions).values(component);

      // Create default image positioning in componentImages table for image components
      if (defaultComponent.componentType === 'banner' || defaultComponent.componentType === 'profilePicture') {
        const { saveImagePositioning } = await import('@/lib/imageStorage');
        await saveImagePositioning(address, defaultComponent.componentType, '0', {
          scale: 1,
          positionX: 0,
          positionY: 0,
          naturalWidth: null,
          naturalHeight: null
        });
      }
    }


  } catch (error) {
    console.error('Error creating default component positions:', error);
    throw error;
  }
}

/**
 * Create default profile for a new user
 * @param address Wallet address
 * @param chain Blockchain network chain ID
 */
export async function createDefaultProfile(address: string, chain: string = "25"): Promise<void> {
  if (!address) return;

  try {
    console.log(`Creating default profile for address: ${address}`);

    // Get profile defaults from system settings
    const profileDefaults = await getProfileDefaults(chain);

    // Calculate expiry date if default_expiry_days is set
    let expiryDate = null;
    if (profileDefaults.default_expiry_days) {
      const days = parseInt(profileDefaults.default_expiry_days);
      if (!isNaN(days) && days > 0) {
        expiryDate = new Date();
        expiryDate.setDate(expiryDate.getDate() + days);
      }
    }

    // Create profile with values from database defaults
    // Note: name and bio are now stored in the componentPositions table for the profilePicture component
    await db.insert(web3Profile).values({
      address,
      chain,
      role: profileDefaults.default_role,
      status: profileDefaults.default_status,
      expiryDate,
      transactionHash: null,
    });


  } catch (error) {
    console.error('Error creating default profile:', error);
    throw error;
  }
}

/**
 * Ensure default profile and component positions exist for a user
 * @param address Wallet address
 * @param chain Blockchain network chain ID
 */
export async function ensureDefaults(address: string, chain: string = "25"): Promise<void> {
  if (!address) return;

  try {


    // Check if profile exists
    const profileExistsResult = await profileExists(address);

    // If profile doesn't exist, create it
    if (!profileExistsResult) {
      await createDefaultProfile(address, chain);
    } else {

    }

    // Check if component positions exist
    const positionsExist = await componentPositionsExist(address);

    // If component positions don't exist, create them
    if (!positionsExist) {
      await createDefaultComponentPositions(address, chain);
    } else {

    }
  } catch (error) {
    console.error('Error ensuring defaults:', error);
    throw error;
  }
}

/**
 * Get profile data with components for the given address
 * If profile doesn't exist, create default profile and components
 * @param address Wallet address
 * @returns Profile data with components
 */
export async function getOrCreateProfile(address: string): Promise<any> {
  if (!address) throw new Error('Address is required');

  try {
    console.log(`Getting or creating profile for address: ${address}`);

    // Ensure defaults exist
    await ensureDefaults(address);

    // Get profile data
    const profile = await db
      .select()
      .from(web3Profile)
      .where(eq(web3Profile.address, address));

    if (profile.length === 0) {
      throw new Error('Failed to create or retrieve profile');
    }

    // Get component positions
    const components = await db
      .select()
      .from(componentPositions)
      .where(eq(componentPositions.address, address))
      .orderBy(componentPositions.order);

    console.log(`Found ${components.length} components for address: ${address}`);

    // Get component images for positioning data
    const componentImagesMap = new Map();
    for (const component of components) {
      if (component.componentType === 'banner' || component.componentType === 'profilePicture') {
        const images = await getComponentImages(address, component.componentType);
        if (images.length > 0) {
          componentImagesMap.set(component.componentType, images[0]);
        }
      }
    }

    // Return profile data with components
    return {
      ...profile[0],
      components: components.map(c => {
        const details: {
          backgroundColor?: string;
          fontColor?: string | null;
          socialLinks?: any;
          profileName?: string;
          profileBio?: string;
          [key: string]: any;
        } = c.details || {};
        const image = componentImagesMap.get(c.componentType);

        return {
          componentType: c.componentType,
          order: c.order,
          hidden: c.hidden,
          backgroundColor: details.backgroundColor,
          fontColor: details.fontColor,
          position: image ? { x: image.positionX, y: image.positionY } : { x: 0, y: 0 },
          details: c.details // Include the full details object
        };
      }),
    };
  } catch (error) {
    console.error('Error getting or creating profile:', error);
    throw error;
  }
}
