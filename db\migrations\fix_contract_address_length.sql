-- Migration to fix contract address and chain ID length issues
-- This increases the length of contract_address, holder_address, and chain_id fields to support longer blockchain addresses
-- Run this SQL in your MySQL database

-- Update token_holder_snapshots table
ALTER TABLE `token_holder_snapshots`
MODIFY COLUMN `contract_address` varchar(70) NOT NULL,
MODIFY COLUMN `chain_id` varchar(40) NOT NULL;

-- Update token_holders table
ALTER TABLE `token_holders`
MODIFY COLUMN `holder_address` varchar(70) NOT NULL;

-- Update token_holder_jobs table
ALTER TABLE `token_holder_jobs`
MODIFY COLUMN `contract_address` varchar(70) NOT NULL,
MODIFY COLUMN `chain_id` varchar(40) NOT NULL;
