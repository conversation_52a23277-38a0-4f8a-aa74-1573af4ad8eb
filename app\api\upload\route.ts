import { NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { componentPositions, web3Profile } from '@/db/schema';
import { blobToBase64, getMimeTypeFromExtension } from '@/lib/imageUtils';
import { eq, and } from 'drizzle-orm';
import { saveImage, deleteComponentImages } from '@/lib/imageStorage';
import { getComponentDefaults, getProfileDefaults } from '@/app/utils/systemSettings';

export async function POST(request: Request): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    const filename = searchParams.get('filename');

    if (!filename) {
      return NextResponse.json(
        { error: 'Filename is required' },
        { status: 400 }
      );
    }

    const parts = filename.split('_');
    if (parts.length < 2) {
      return NextResponse.json(
        { error: 'Invalid filename format. Expected: {address}_{type}.{extension}' },
        { status: 400 }
      );
    }

    const address = parts[0];

    // Handle special case for bannerpfp_profile
    let type;
    let typeWithExt;

    if (parts.length >= 3 && parts[1] === 'bannerpfp' && parts[2].includes('.')) {
      // Format: {address}_bannerpfp_profile.{extension}
      type = 'bannerpfp_profile';
      typeWithExt = parts[2];
    } else if (parts.length >= 3 && parts[1] === 'bannerpfp' && parts[2] === 'banner') {
      // Format: {address}_bannerpfp_banner.{extension}
      type = 'bannerpfp_banner';
      typeWithExt = parts[3] || '';
    } else {
      // Standard format: {address}_{type}.{extension}
      typeWithExt = parts[1];
      type = typeWithExt.split('.')[0];
    }
    const extension = typeWithExt.split('.')[1] || 'jpg';

    try {
      const file = await request.blob();
      const base64Data = await blobToBase64(file);
      const mimeType = getMimeTypeFromExtension(extension);
      const dataUrl = `data:${mimeType};base64,${base64Data}`;
      const now = new Date();

      if (type === 'bannerpfp_banner') {
        // Get the chain from web3Profile
        const profileData = await db.select().from(web3Profile).where(eq(web3Profile.address, address));
        const chain = profileData.length > 0 ? profileData[0].chain : undefined;

        // Delete any existing bannerpfp banner images
        await deleteComponentImages(address, 'bannerpfp', 'banner');

        // Save the new bannerpfp banner image
        await saveImage(address, 'bannerpfp', 'banner', base64Data);

        // Check if we have a bannerpfp component record
        const existingRecord = await db.select().from(componentPositions).where(
          and(
            eq(componentPositions.address, address),
            eq(componentPositions.componentType, 'bannerpfp')
          )
        );

        if (existingRecord.length > 0) {
          // Update the existing record (without the image data)
          await db.update(componentPositions)
            .set({
              // Update timestamp only
              updatedAt: now
            })
            .where(
              and(
                eq(componentPositions.address, address),
                eq(componentPositions.componentType, 'bannerpfp')
              )
            );
        } else {
          // Get component defaults from system settings
          const componentDefaults = await getComponentDefaults(chain || '25');
          const profileDefaults = await getProfileDefaults(chain || '25');
          const bannerpfpDefaults = componentDefaults.find(comp => comp.componentType === 'bannerpfp');

          // Format the default profile name using the template from settings
          const defaultNameFormat = profileDefaults.default_profile_name_format || 'Web3 User {address}';
          const formattedName = defaultNameFormat.replace('{address}', address.substring(0, 8));
          const defaultBio = profileDefaults.default_profile_bio || '';

          // Use profileNameStyle from system settings if available, otherwise use default
          const defaultProfileNameStyle = bannerpfpDefaults?.details?.profileNameStyle || {
            fontSize: '1.5rem',
            fontWeight: 'bold',
            fontColor: '#ffffff',
            effect: 'typewriter'
          };

          // Insert a new record with details field
          await db.insert(componentPositions).values({
            address: address,
            chain: chain || '25', // Ensure chain is never undefined
            componentType: 'bannerpfp',
            order: bannerpfpDefaults?.order || '5',
            hidden: bannerpfpDefaults?.hidden || 'N',
            details: {
              backgroundColor: bannerpfpDefaults?.details?.backgroundColor || 'transparent',
              fontColor: bannerpfpDefaults?.details?.fontColor || null,
              profileShape: bannerpfpDefaults?.details?.profileShape || 'circular',
              profileHorizontalPosition: bannerpfpDefaults?.details?.profileHorizontalPosition || 50,
              profileNameHorizontalPosition: bannerpfpDefaults?.details?.profileNameHorizontalPosition || 50,
              profileNameStyle: defaultProfileNameStyle,
              profileName: bannerpfpDefaults?.details?.profileName || formattedName,
              profileBio: bannerpfpDefaults?.details?.profileBio || defaultBio
            },
            createdAt: now,
            updatedAt: now
          });
        }
      } else if (type === 'bannerpfp_profile') {
        // Get the chain from web3Profile
        const profileData = await db.select().from(web3Profile).where(eq(web3Profile.address, address));
        const chain = profileData.length > 0 ? profileData[0].chain : undefined;

        // Delete any existing bannerpfp profile images
        await deleteComponentImages(address, 'bannerpfp', 'profile');

        // Save the new bannerpfp profile image
        await saveImage(address, 'bannerpfp', 'profile', base64Data);

        // Check if we have a bannerpfp component record
        const existingRecord = await db.select().from(componentPositions).where(
          and(
            eq(componentPositions.address, address),
            eq(componentPositions.componentType, 'bannerpfp')
          )
        );

        if (existingRecord.length > 0) {
          // Update the existing record (without the image data)
          await db.update(componentPositions)
            .set({
              // Update timestamp only
              updatedAt: now
            })
            .where(
              and(
                eq(componentPositions.address, address),
                eq(componentPositions.componentType, 'bannerpfp')
              )
            );
        } else {
          // Get component defaults from system settings
          const componentDefaults = await getComponentDefaults(chain || '25');
          const profileDefaults = await getProfileDefaults(chain || '25');
          const bannerpfpDefaults = componentDefaults.find(comp => comp.componentType === 'bannerpfp');

          // Format the default profile name using the template from settings
          const defaultNameFormat = profileDefaults.default_profile_name_format || 'Web3 User {address}';
          const formattedName = defaultNameFormat.replace('{address}', address.substring(0, 8));
          const defaultBio = profileDefaults.default_profile_bio || '';

          // Use profileNameStyle from system settings if available, otherwise use default
          const defaultProfileNameStyle = bannerpfpDefaults?.details?.profileNameStyle || {
            fontSize: '1.5rem',
            fontWeight: 'bold',
            fontColor: '#ffffff',
            effect: 'typewriter'
          };

          // Insert a new record with details field
          await db.insert(componentPositions).values({
            address: address,
            chain: chain || '25', // Ensure chain is never undefined
            componentType: 'bannerpfp',
            order: bannerpfpDefaults?.order || '5',
            hidden: bannerpfpDefaults?.hidden || 'N',
            details: {
              backgroundColor: bannerpfpDefaults?.details?.backgroundColor || 'transparent',
              fontColor: bannerpfpDefaults?.details?.fontColor || null,
              profileShape: bannerpfpDefaults?.details?.profileShape || 'circular',
              profileHorizontalPosition: bannerpfpDefaults?.details?.profileHorizontalPosition || 50,
              profileNameHorizontalPosition: bannerpfpDefaults?.details?.profileNameHorizontalPosition || 50,
              profileNameStyle: defaultProfileNameStyle,
              profileName: bannerpfpDefaults?.details?.profileName || formattedName,
              profileBio: bannerpfpDefaults?.details?.profileBio || defaultBio
            },
            createdAt: now,
            updatedAt: now
          });
        }
      } else {
        return NextResponse.json(
          { error: 'Invalid image type. Expected "bannerpfp_banner" or "bannerpfp_profile"' },
          { status: 400 }
        );
      }

      return NextResponse.json({
        url: dataUrl,
        pathname: filename,
        contentType: mimeType,
        contentDisposition: `inline; filename="${filename}"`,
        size: file.size
      });
    } catch (uploadError) {

      throw uploadError;
    }
  } catch (error) {

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to upload file' },
      { status: 500 }
    );
  }
}

