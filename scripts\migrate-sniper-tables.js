const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

async function migrateSniperTables() {
  let connection;
  
  try {
    // Create connection using environment variables
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'web3socials',
      port: process.env.DB_PORT || 3306
    });

    console.log('Connected to database');

    // Read the migration SQL file
    const migrationPath = path.join(__dirname, '..', 'db', 'migrations', 'add_sniper_tables.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    // Split the SQL into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    console.log(`Executing ${statements.length} SQL statements...`);

    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      console.log(`Executing statement ${i + 1}/${statements.length}...`);
      
      try {
        await connection.execute(statement);
        console.log(`✓ Statement ${i + 1} executed successfully`);
      } catch (error) {
        console.error(`✗ Error executing statement ${i + 1}:`, error.message);
        throw error;
      }
    }

    console.log('✅ Sniper tables migration completed successfully!');
    console.log('📋 Created tables:');
    console.log('   • sniper_creators - Token creator addresses for monitoring');
    console.log('   • sniper_watched_addresses - Specific addresses to watch');
    console.log('   • sniper_configs - User sniper configurations');

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('Database connection closed');
    }
  }
}

// Run the migration
migrateSniperTables();
