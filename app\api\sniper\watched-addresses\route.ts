import { NextRequest } from 'next/server';
import { db } from '@/db/drizzle';
import { sniperWatchedAddresses } from '@/db/schema';
import { eq, and } from 'drizzle-orm';

// GET - Fetch all watched addresses for a specific chain
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const chainId = searchParams.get('chainId');

    if (!chainId) {
      return Response.json(
        { error: 'Chain ID is required' },
        { status: 400 }
      );
    }

    const watchedAddresses = await db
      .select()
      .from(sniperWatchedAddresses)
      .where(eq(sniperWatchedAddresses.chainId, chainId))
      .orderBy(sniperWatchedAddresses.name);

    return Response.json({
      success: true,
      watchedAddresses
    });

  } catch (error) {
    console.error('Error fetching watched addresses:', error);
    return Response.json(
      { error: 'Failed to fetch watched addresses' },
      { status: 500 }
    );
  }
}

// POST - Add a new watched address
export async function POST(request: NextRequest) {
  try {
    const { address, name, chainId } = await request.json();

    // Validate required fields
    if (!address || !chainId) {
      return Response.json(
        { error: 'Address and chain ID are required' },
        { status: 400 }
      );
    }

    // Validate address format
    if (!address.match(/^0x[a-fA-F0-9]{40}$/)) {
      return Response.json(
        { error: 'Invalid address format' },
        { status: 400 }
      );
    }

    // Check if watched address already exists
    const existingAddress = await db
      .select()
      .from(sniperWatchedAddresses)
      .where(and(
        eq(sniperWatchedAddresses.address, address),
        eq(sniperWatchedAddresses.chainId, chainId)
      ))
      .limit(1);

    if (existingAddress.length > 0) {
      return Response.json(
        { error: 'Watched address already exists for this chain' },
        { status: 409 }
      );
    }

    // Insert new watched address
    const result = await db
      .insert(sniperWatchedAddresses)
      .values({
        address,
        name: name || null,
        chainId,
        isActive: 'Y'
      });

    return Response.json({
      success: true,
      message: 'Watched address added successfully',
      id: result[0].insertId
    });

  } catch (error) {
    console.error('Error adding watched address:', error);
    return Response.json(
      { error: 'Failed to add watched address' },
      { status: 500 }
    );
  }
}

// PUT - Update a watched address
export async function PUT(request: NextRequest) {
  try {
    const { id, name, isActive } = await request.json();

    if (!id) {
      return Response.json(
        { error: 'Watched address ID is required' },
        { status: 400 }
      );
    }

    const updateData: any = {};
    if (name !== undefined) updateData.name = name;
    if (isActive !== undefined) updateData.isActive = isActive;
    updateData.updatedAt = new Date();

    await db
      .update(sniperWatchedAddresses)
      .set(updateData)
      .where(eq(sniperWatchedAddresses.id, id));

    return Response.json({
      success: true,
      message: 'Watched address updated successfully'
    });

  } catch (error) {
    console.error('Error updating watched address:', error);
    return Response.json(
      { error: 'Failed to update watched address' },
      { status: 500 }
    );
  }
}

// DELETE - Remove a watched address
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return Response.json(
        { error: 'Watched address ID is required' },
        { status: 400 }
      );
    }

    await db
      .delete(sniperWatchedAddresses)
      .where(eq(sniperWatchedAddresses.id, parseInt(id)));

    return Response.json({
      success: true,
      message: 'Watched address removed successfully'
    });

  } catch (error) {
    console.error('Error removing watched address:', error);
    return Response.json(
      { error: 'Failed to remove watched address' },
      { status: 500 }
    );
  }
}
