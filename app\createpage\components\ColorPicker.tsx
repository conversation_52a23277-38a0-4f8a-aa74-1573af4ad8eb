'use client';

import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Check, Paintbrush, Palette } from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface ColorPickerProps {
  address: string;
  componentType: string;
  onColorChange?: (color: string) => void;
  chain?: string;
}

// Color palette with multiple shades for each color
const colorPalette = {
  special: [
    { name: 'Transparent', value: 'transparent' },
    { name: 'Black', value: '#000000' },
    { name: 'White', value: '#ffffff' },
  ],
  gray: [
    { name: 'Gray-50', value: '#f9fafb' },
    { name: 'Gray-100', value: '#f3f4f6' },
    { name: 'Gray-200', value: '#e5e7eb' },
    { name: 'Gray-300', value: '#d1d5db' },
    { name: 'Gray-400', value: '#9ca3af' },
    { name: 'Gray-500', value: '#6b7280' },
    { name: 'Gray-600', value: '#4b5563' },
    { name: 'Gray-700', value: '#374151' },
    { name: 'Gray-800', value: '#1f2937' },
    { name: 'Gray-900', value: '#111827' },
    { name: 'Gray-950', value: '#030712' },
  ],
  red: [
    { name: 'Red-50', value: '#fef2f2' },
    { name: 'Red-100', value: '#fee2e2' },
    { name: 'Red-200', value: '#fecaca' },
    { name: 'Red-300', value: '#fca5a5' },
    { name: 'Red-400', value: '#f87171' },
    { name: 'Red-500', value: '#ef4444' },
    { name: 'Red-600', value: '#dc2626' },
    { name: 'Red-700', value: '#b91c1c' },
    { name: 'Red-800', value: '#991b1b' },
    { name: 'Red-900', value: '#7f1d1d' },
    { name: 'Red-950', value: '#450a0a' },
  ],
  orange: [
    { name: 'Orange-50', value: '#fff7ed' },
    { name: 'Orange-100', value: '#ffedd5' },
    { name: 'Orange-200', value: '#fed7aa' },
    { name: 'Orange-300', value: '#fdba74' },
    { name: 'Orange-400', value: '#fb923c' },
    { name: 'Orange-500', value: '#f97316' },
    { name: 'Orange-600', value: '#ea580c' },
    { name: 'Orange-700', value: '#c2410c' },
    { name: 'Orange-800', value: '#9a3412' },
    { name: 'Orange-900', value: '#7c2d12' },
    { name: 'Orange-950', value: '#431407' },
  ],
  yellow: [
    { name: 'Yellow-50', value: '#fefce8' },
    { name: 'Yellow-100', value: '#fef9c3' },
    { name: 'Yellow-200', value: '#fef08a' },
    { name: 'Yellow-300', value: '#fde047' },
    { name: 'Yellow-400', value: '#facc15' },
    { name: 'Yellow-500', value: '#eab308' },
    { name: 'Yellow-600', value: '#ca8a04' },
    { name: 'Yellow-700', value: '#a16207' },
    { name: 'Yellow-800', value: '#854d0e' },
    { name: 'Yellow-900', value: '#713f12' },
    { name: 'Yellow-950', value: '#422006' },
  ],
  green: [
    { name: 'Green-50', value: '#f0fdf4' },
    { name: 'Green-100', value: '#dcfce7' },
    { name: 'Green-200', value: '#bbf7d0' },
    { name: 'Green-300', value: '#86efac' },
    { name: 'Green-400', value: '#4ade80' },
    { name: 'Green-500', value: '#22c55e' },
    { name: 'Green-600', value: '#16a34a' },
    { name: 'Green-700', value: '#15803d' },
    { name: 'Green-800', value: '#166534' },
    { name: 'Green-900', value: '#14532d' },
    { name: 'Green-950', value: '#052e16' },
  ],
  blue: [
    { name: 'Blue-50', value: '#eff6ff' },
    { name: 'Blue-100', value: '#dbeafe' },
    { name: 'Blue-200', value: '#bfdbfe' },
    { name: 'Blue-300', value: '#93c5fd' },
    { name: 'Blue-400', value: '#60a5fa' },
    { name: 'Blue-500', value: '#3b82f6' },
    { name: 'Blue-600', value: '#2563eb' },
    { name: 'Blue-700', value: '#1d4ed8' },
    { name: 'Blue-800', value: '#1e40af' },
    { name: 'Blue-900', value: '#1e3a8a' },
    { name: 'Blue-950', value: '#172554' },
  ],
  indigo: [
    { name: 'Indigo-50', value: '#eef2ff' },
    { name: 'Indigo-100', value: '#e0e7ff' },
    { name: 'Indigo-200', value: '#c7d2fe' },
    { name: 'Indigo-300', value: '#a5b4fc' },
    { name: 'Indigo-400', value: '#818cf8' },
    { name: 'Indigo-500', value: '#6366f1' },
    { name: 'Indigo-600', value: '#4f46e5' },
    { name: 'Indigo-700', value: '#4338ca' },
    { name: 'Indigo-800', value: '#3730a3' },
    { name: 'Indigo-900', value: '#312e81' },
    { name: 'Indigo-950', value: '#1e1b4b' },
  ],
  purple: [
    { name: 'Purple-50', value: '#faf5ff' },
    { name: 'Purple-100', value: '#f3e8ff' },
    { name: 'Purple-200', value: '#e9d5ff' },
    { name: 'Purple-300', value: '#d8b4fe' },
    { name: 'Purple-400', value: '#c084fc' },
    { name: 'Purple-500', value: '#a855f7' },
    { name: 'Purple-600', value: '#9333ea' },
    { name: 'Purple-700', value: '#7e22ce' },
    { name: 'Purple-800', value: '#6b21a8' },
    { name: 'Purple-900', value: '#581c87' },
    { name: 'Purple-950', value: '#3b0764' },
  ],
  pink: [
    { name: 'Pink-50', value: '#fdf2f8' },
    { name: 'Pink-100', value: '#fce7f3' },
    { name: 'Pink-200', value: '#fbcfe8' },
    { name: 'Pink-300', value: '#f9a8d4' },
    { name: 'Pink-400', value: '#f472b6' },
    { name: 'Pink-500', value: '#ec4899' },
    { name: 'Pink-600', value: '#db2777' },
    { name: 'Pink-700', value: '#be185d' },
    { name: 'Pink-800', value: '#9d174d' },
    { name: 'Pink-900', value: '#831843' },
    { name: 'Pink-950', value: '#500724' },
  ],
};

// Flatten the palette for the color picker
const colorOptions = [
  ...colorPalette.special,
  ...colorPalette.gray,
  ...colorPalette.red,
  ...colorPalette.orange,
  ...colorPalette.yellow,
  ...colorPalette.green,
  ...colorPalette.blue,
  ...colorPalette.indigo,
  ...colorPalette.purple,
  ...colorPalette.pink,
];

export default function ColorPicker({ address, componentType, onColorChange, chain = '25' }: ColorPickerProps) {
  const [selectedColor, setSelectedColor] = useState('transparent');
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('special');

  // Use a ref to store the latest onColorChange callback
  const onColorChangeRef = useRef(onColorChange);

  // Update the ref when onColorChange changes
  useEffect(() => {
    onColorChangeRef.current = onColorChange;
  }, [onColorChange]);

  // Track if initial load has been done
  const initialLoadDoneRef = useRef(false);

  // Load the current background color when the component mounts
  useEffect(() => {
    const loadBackgroundColor = async () => {
      try {
        // Skip if we've already loaded once
        if (initialLoadDoneRef.current) return;

        setIsLoading(true);
        const response = await fetch(`/api/component-background/${address}?componentType=${componentType}`);

        if (response.ok) {
          const data = await response.json();
          const newColor = data.backgroundColor || 'transparent';

          // Only update if the color is different
          if (newColor !== selectedColor) {
            setSelectedColor(newColor);

            // Only call the callback during initial load
            if (!initialLoadDoneRef.current && onColorChangeRef.current) {
              onColorChangeRef.current(newColor);
            }
          }

          // Mark initial load as done
          initialLoadDoneRef.current = true;
        }
      } catch (error) {
        console.error('Failed to load background color:', error);
      } finally {
        setIsLoading(false);
      }
    };

    if (address && componentType) {
      loadBackgroundColor();
    }
  }, [address, componentType, selectedColor]);

  // Save the selected color to the database
  const saveBackgroundColor = async (color: string) => {
    // Skip if the color is the same
    if (color === selectedColor) return;

    try {
      setIsSaving(true);
      const response = await fetch(`/api/component-background/${address}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          componentType,
          backgroundColor: color,
          chain,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to save background color');
      }

      // Update local state
      setSelectedColor(color);

      // Call the parent callback
      if (onColorChangeRef.current) {
        onColorChangeRef.current(color);
      }

      toast.success('Background color updated');
    } catch (error) {
      console.error('Failed to save background color:', error);
      toast.error('Failed to save background color');
    } finally {
      setIsSaving(false);
    }
  };

  // Handle color selection
  const handleColorSelect = (color: string) => {
    saveBackgroundColor(color);
  };

  return (
    <div className="absolute top-2 left-2 z-10">
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            size="icon"
            className="h-6 w-6 rounded-full"
            style={{
              backgroundColor: selectedColor,
              borderColor: selectedColor,
            }}
            disabled={isLoading || isSaving}
          >
            <Paintbrush className="h-3 w-3 text-white" />
            <span className="sr-only">Pick a color</span>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80 p-3" align="start">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="font-medium text-sm">Background Color</h4>
              <div className="flex items-center space-x-1">
                <Palette className="h-4 w-4 text-muted-foreground" />
                <span className="text-xs text-muted-foreground">{colorOptions.length} colors</span>
              </div>
            </div>

            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid grid-cols-5 h-8 w-full">
                <TabsTrigger value="special" className="text-xs">Basic</TabsTrigger>
                <TabsTrigger value="gray" className="text-xs">Gray</TabsTrigger>
                <TabsTrigger value="warm" className="text-xs">Warm</TabsTrigger>
                <TabsTrigger value="cool" className="text-xs">Cool</TabsTrigger>
                <TabsTrigger value="vibrant" className="text-xs">Vibrant</TabsTrigger>
              </TabsList>

              {/* Special colors (transparent, black, white) */}
              <TabsContent value="special" className="mt-2">
                <div className="grid grid-cols-3 gap-2">
                  {colorPalette.special.map((color) => (
                    <Button
                      key={color.value}
                      variant="outline"
                      className={cn(
                        "h-8 w-full rounded-md p-0 border flex flex-col items-center justify-center",
                        selectedColor === color.value && "ring-2 ring-offset-2"
                      )}
                      style={{
                        backgroundColor: color.value === 'transparent' ? 'rgba(0,0,0,0.2)' : color.value,
                        borderColor: color.value === 'transparent' ? 'rgba(255,255,255,0.2)' : color.value,
                      }}
                      onClick={() => handleColorSelect(color.value)}
                      disabled={isSaving}
                    >
                      <span className="text-[10px] text-foreground">{color.name}</span>
                      {selectedColor === color.value && (
                        <Check className="h-3 w-3 text-white absolute top-1 right-1" />
                      )}
                    </Button>
                  ))}
                </div>
              </TabsContent>

              {/* Gray colors */}
              <TabsContent value="gray" className="mt-2">
                <div className="grid grid-cols-5 gap-1">
                  {colorPalette.gray.map((color) => (
                    <Button
                      key={color.value}
                      variant="outline"
                      className={cn(
                        "h-8 w-full rounded-md p-0 border",
                        selectedColor === color.value && "ring-2 ring-offset-2"
                      )}
                      style={{
                        backgroundColor: color.value,
                        borderColor: color.value,
                      }}
                      onClick={() => handleColorSelect(color.value)}
                      disabled={isSaving}
                    >
                      <span className="sr-only">{color.name}</span>
                      {selectedColor === color.value && (
                        <Check className="h-3 w-3 text-white" />
                      )}
                    </Button>
                  ))}
                </div>
              </TabsContent>

              {/* Warm colors (red, orange, yellow) */}
              <TabsContent value="warm" className="mt-2">
                <div className="space-y-2">
                  <div>
                    <h5 className="text-xs font-medium mb-1">Red</h5>
                    <div className="grid grid-cols-5 gap-1">
                      {colorPalette.red.map((color) => (
                        <Button
                          key={color.value}
                          variant="outline"
                          className={cn(
                            "h-8 w-full rounded-md p-0 border",
                            selectedColor === color.value && "ring-2 ring-offset-2"
                          )}
                          style={{
                            backgroundColor: color.value,
                            borderColor: color.value,
                          }}
                          onClick={() => handleColorSelect(color.value)}
                          disabled={isSaving}
                        >
                          <span className="sr-only">{color.name}</span>
                          {selectedColor === color.value && (
                            <Check className="h-3 w-3 text-white" />
                          )}
                        </Button>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h5 className="text-xs font-medium mb-1">Orange</h5>
                    <div className="grid grid-cols-5 gap-1">
                      {colorPalette.orange.map((color) => (
                        <Button
                          key={color.value}
                          variant="outline"
                          className={cn(
                            "h-8 w-full rounded-md p-0 border",
                            selectedColor === color.value && "ring-2 ring-offset-2"
                          )}
                          style={{
                            backgroundColor: color.value,
                            borderColor: color.value,
                          }}
                          onClick={() => handleColorSelect(color.value)}
                          disabled={isSaving}
                        >
                          <span className="sr-only">{color.name}</span>
                          {selectedColor === color.value && (
                            <Check className="h-3 w-3 text-white" />
                          )}
                        </Button>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h5 className="text-xs font-medium mb-1">Yellow</h5>
                    <div className="grid grid-cols-5 gap-1">
                      {colorPalette.yellow.map((color) => (
                        <Button
                          key={color.value}
                          variant="outline"
                          className={cn(
                            "h-8 w-full rounded-md p-0 border",
                            selectedColor === color.value && "ring-2 ring-offset-2"
                          )}
                          style={{
                            backgroundColor: color.value,
                            borderColor: color.value,
                          }}
                          onClick={() => handleColorSelect(color.value)}
                          disabled={isSaving}
                        >
                          <span className="sr-only">{color.name}</span>
                          {selectedColor === color.value && (
                            <Check className="h-3 w-3 text-white" />
                          )}
                        </Button>
                      ))}
                    </div>
                  </div>
                </div>
              </TabsContent>

              {/* Cool colors (green, blue, indigo) */}
              <TabsContent value="cool" className="mt-2">
                <div className="space-y-2">
                  <div>
                    <h5 className="text-xs font-medium mb-1">Green</h5>
                    <div className="grid grid-cols-5 gap-1">
                      {colorPalette.green.map((color) => (
                        <Button
                          key={color.value}
                          variant="outline"
                          className={cn(
                            "h-8 w-full rounded-md p-0 border",
                            selectedColor === color.value && "ring-2 ring-offset-2"
                          )}
                          style={{
                            backgroundColor: color.value,
                            borderColor: color.value,
                          }}
                          onClick={() => handleColorSelect(color.value)}
                          disabled={isSaving}
                        >
                          <span className="sr-only">{color.name}</span>
                          {selectedColor === color.value && (
                            <Check className="h-3 w-3 text-white" />
                          )}
                        </Button>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h5 className="text-xs font-medium mb-1">Blue</h5>
                    <div className="grid grid-cols-5 gap-1">
                      {colorPalette.blue.map((color) => (
                        <Button
                          key={color.value}
                          variant="outline"
                          className={cn(
                            "h-8 w-full rounded-md p-0 border",
                            selectedColor === color.value && "ring-2 ring-offset-2"
                          )}
                          style={{
                            backgroundColor: color.value,
                            borderColor: color.value,
                          }}
                          onClick={() => handleColorSelect(color.value)}
                          disabled={isSaving}
                        >
                          <span className="sr-only">{color.name}</span>
                          {selectedColor === color.value && (
                            <Check className="h-3 w-3 text-white" />
                          )}
                        </Button>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h5 className="text-xs font-medium mb-1">Indigo</h5>
                    <div className="grid grid-cols-5 gap-1">
                      {colorPalette.indigo.map((color) => (
                        <Button
                          key={color.value}
                          variant="outline"
                          className={cn(
                            "h-8 w-full rounded-md p-0 border",
                            selectedColor === color.value && "ring-2 ring-offset-2"
                          )}
                          style={{
                            backgroundColor: color.value,
                            borderColor: color.value,
                          }}
                          onClick={() => handleColorSelect(color.value)}
                          disabled={isSaving}
                        >
                          <span className="sr-only">{color.name}</span>
                          {selectedColor === color.value && (
                            <Check className="h-3 w-3 text-white" />
                          )}
                        </Button>
                      ))}
                    </div>
                  </div>
                </div>
              </TabsContent>

              {/* Vibrant colors (purple, pink) */}
              <TabsContent value="vibrant" className="mt-2">
                <div className="space-y-2">
                  <div>
                    <h5 className="text-xs font-medium mb-1">Purple</h5>
                    <div className="grid grid-cols-5 gap-1">
                      {colorPalette.purple.map((color) => (
                        <Button
                          key={color.value}
                          variant="outline"
                          className={cn(
                            "h-8 w-full rounded-md p-0 border",
                            selectedColor === color.value && "ring-2 ring-offset-2"
                          )}
                          style={{
                            backgroundColor: color.value,
                            borderColor: color.value,
                          }}
                          onClick={() => handleColorSelect(color.value)}
                          disabled={isSaving}
                        >
                          <span className="sr-only">{color.name}</span>
                          {selectedColor === color.value && (
                            <Check className="h-3 w-3 text-white" />
                          )}
                        </Button>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h5 className="text-xs font-medium mb-1">Pink</h5>
                    <div className="grid grid-cols-5 gap-1">
                      {colorPalette.pink.map((color) => (
                        <Button
                          key={color.value}
                          variant="outline"
                          className={cn(
                            "h-8 w-full rounded-md p-0 border",
                            selectedColor === color.value && "ring-2 ring-offset-2"
                          )}
                          style={{
                            backgroundColor: color.value,
                            borderColor: color.value,
                          }}
                          onClick={() => handleColorSelect(color.value)}
                          disabled={isSaving}
                        >
                          <span className="sr-only">{color.name}</span>
                          {selectedColor === color.value && (
                            <Check className="h-3 w-3 text-white" />
                          )}
                        </Button>
                      ))}
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}
