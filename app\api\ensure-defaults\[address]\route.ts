import { NextRequest } from 'next/server';
import { createProfile } from '@/app/actions/profileActions';

type Context = {
  params: Promise<{
    address: string;
  }>;
};

// Use a simple in-memory cache to prevent duplicate calls
const processingAddresses = new Set<string>();

export async function GET(
  request: NextRequest,
  context: Context
): Promise<Response> {
  try {
    const { address } = await context.params;
    const chain = request.nextUrl.searchParams.get('chain');

    if (!address) {
      return Response.json(
        { error: 'Address is required' },
        { status: 400 }
      );
    }

    // Check if we're already processing this address
    const cacheKey = `${address}-${chain || '25'}`;
    if (processingAddresses.has(cacheKey)) {
      return Response.json({ success: true, cached: true });
    }

    // Add to processing set
    processingAddresses.add(cacheKey);

    try {
      // Create profile if it doesn't exist
      await createProfile(address, chain || undefined);

      // Return success response
      return Response.json({ success: true });
    } finally {
      // Remove from processing set when done
      processingAddresses.delete(cacheKey);
    }
  } catch (error) {
    console.error('Failed to ensure defaults:', error);
    return Response.json(
      { error: 'Failed to ensure defaults' },
      { status: 500 }
    );
  }
}
