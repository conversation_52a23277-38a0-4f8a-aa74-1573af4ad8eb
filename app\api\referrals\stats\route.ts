import { NextRequest } from 'next/server';
import { getReferralStats } from '@/app/utils/referralUtils';
import { handleApiError } from '@/app/api/errorHandler';

export async function GET(request: NextRequest): Promise<Response> {
  try {
    const { searchParams } = new URL(request.url);
    const address = searchParams.get('address');

    if (!address) {
      return Response.json(
        { error: 'Address is required' },
        { status: 400 }
      );
    }

    const stats = await getReferralStats(address);

    return Response.json(stats);
  } catch (error) {
    return handleApiError(error);
  }
}
