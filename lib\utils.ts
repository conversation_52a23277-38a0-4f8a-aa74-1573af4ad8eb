import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Detect the format of a wallet address
 */
export function detectAddressFormat(address: string): 'ethereum' | 'solana' | 'unknown' {
  if (!address) return 'unknown';

  // Ethereum addresses: 0x followed by 40 hex characters
  if (/^0x[a-fA-F0-9]{40}$/.test(address)) {
    return 'ethereum';
  }

  // Solana addresses: Base58 encoded, typically 32-44 characters
  if (/^[1-9A-HJ-NP-Za-km-z]{32,44}$/.test(address)) {
    return 'solana';
  }

  return 'unknown';
}

/**
 * Validate if an address is a valid Ethereum address
 */
export function isValidEthereumAddress(address: string): boolean {
  return detectAddressFormat(address) === 'ethereum';
}

/**
 * Validate if an address is a valid Solana address
 */
export function isValidSolanaAddress(address: string): boolean {
  return detectAddressFormat(address) === 'solana';
}

/**
 * Safely handle address validation errors from wallet extensions
 */
export function handleAddressValidationError(error: any, address?: string): void {
  if (error?.message?.includes('InvalidAddressError') && address) {
    // Don't throw the error for address validation errors
    return;
  }

  // Re-throw other errors
  throw error;
}
