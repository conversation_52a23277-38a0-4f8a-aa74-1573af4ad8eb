import { NextRequest } from 'next/server';
import { 
  getLatestSnapshot, 
  getSnapshotHolders, 
  getTokenSnapshots 
} from '@/lib/tokenHolderUtils';

type Context = {
  params: Promise<{
    contractAddress: string;
  }>;
};

export async function GET(
  request: NextRequest,
  context: Context
): Promise<Response> {
  try {
    const { contractAddress } = await context.params;
    const { searchParams } = new URL(request.url);
    const chainId = searchParams.get('chainId') || '25'; // Default to Cronos
    const snapshotId = searchParams.get('snapshotId');
    const includeHolders = searchParams.get('includeHolders') === 'true';
    const history = searchParams.get('history') === 'true';

    if (!contractAddress) {
      return Response.json(
        { error: 'Contract address is required' },
        { status: 400 }
      );
    }

    // If requesting history, return all snapshots
    if (history) {
      const snapshots = await getTokenSnapshots(contractAddress, chainId);
      return Response.json({
        contractAddress,
        chainId,
        snapshots,
        total: snapshots.length
      });
    }

    // Get specific snapshot or latest
    let snapshot;
    if (snapshotId) {
      // Get specific snapshot by ID
      const snapshots = await getTokenSnapshots(contractAddress, chainId);
      snapshot = snapshots.find(s => s.id === parseInt(snapshotId));
      
      if (!snapshot) {
        return Response.json(
          { error: 'Snapshot not found' },
          { status: 404 }
        );
      }
    } else {
      // Get latest snapshot
      snapshot = await getLatestSnapshot(contractAddress, chainId);
      
      if (!snapshot) {
        return Response.json(
          { error: 'No snapshots found for this token' },
          { status: 404 }
        );
      }
    }

    const response: any = {
      contractAddress,
      chainId,
      snapshot: {
        id: snapshot.id,
        tokenName: snapshot.tokenName,
        snapshotDate: snapshot.snapshotDate.toISOString().split('T')[0], // Convert Date to YYYY-MM-DD string
        totalHolders: snapshot.totalHolders,
        createdAt: snapshot.createdAt?.toISOString() || null
      }
    };

    // Include holder data if requested
    if (includeHolders) {
      const holders = await getSnapshotHolders(snapshot.id);
      response.holders = holders;
    }

    return Response.json(response);

  } catch (error) {
    return Response.json(
      {
        error: 'Failed to retrieve token holder data',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Export CSV data for a snapshot
export async function POST(
  request: NextRequest,
  context: Context
): Promise<Response> {
  try {
    const { contractAddress } = await context.params;
    const { chainId, snapshotId, format } = await request.json();

    if (!contractAddress || !chainId) {
      return Response.json(
        { error: 'Contract address and chain ID are required' },
        { status: 400 }
      );
    }

    // Get snapshot
    let snapshot;
    if (snapshotId) {
      const snapshots = await getTokenSnapshots(contractAddress, chainId);
      snapshot = snapshots.find(s => s.id === parseInt(snapshotId));
    } else {
      snapshot = await getLatestSnapshot(contractAddress, chainId);
    }

    if (!snapshot) {
      return Response.json(
        { error: 'Snapshot not found' },
        { status: 404 }
      );
    }

    // Get holders
    const holders = await getSnapshotHolders(snapshot.id);

    if (format === 'csv') {
      // Generate CSV content
      const csvHeader = 'HolderAddress,Balance,PendingBalanceUpdate\n';
      const csvRows = holders.map(holder =>
        `"${holder.holderAddress}","${holder.balance}","${holder.pendingBalanceUpdate || 'No'}"`
      ).join('\n');
      
      const csvContent = csvHeader + csvRows;

      return new Response(csvContent, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="token_holders_${contractAddress}_${snapshot.snapshotDate.toISOString().split('T')[0]}.csv"`
        }
      });
    }

    // Return JSON by default
    return Response.json({
      snapshot,
      holders,
      totalHolders: holders.length
    });

  } catch (error) {
    console.error('Error exporting token holder data:', error);
    return Response.json(
      { 
        error: 'Failed to export token holder data',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
