'use client';

import { useEffect } from 'react';
import { useAppKitAccount } from '@reown/appkit/react';
import { usePathname } from 'next/navigation';

/**
 * Simplified hook for wallet connection status
 * AppKit handles connection persistence automatically
 */
export function useWalletConnectionPersistence() {
  const { isConnected, address } = useAppKitAccount();

  return { isConnected, address };
}
