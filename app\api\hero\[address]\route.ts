import { NextRequest } from 'next/server';
import { db } from '@/db/drizzle';
import { componentPositions } from '@/db/schema';
import { eq, and } from 'drizzle-orm';
import { handleApiError } from '@/app/api/errorHandler';
import { saveImage, getComponentImages, deleteComponentImages, getImageById } from '@/lib/imageStorage';
import { getMimeTypeFromDataUrl } from '@/lib/imageUtils';
import { getComponentDefaults } from '@/app/utils/systemSettings';

// Define the context type that matches Next.js 15 expectations
type Context = {
  params: Promise<{
    address: string;
  }>;
};

// GET endpoint to fetch hero content
export async function GET(
  _request: NextRequest,
  context: Context
): Promise<Response> {
  const params = await context.params;
  if (!params?.address) {
    return Response.json(
      { error: 'Address is required' },
      { status: 400 }
    );
  }

  const address = params.address;

  try {
    // Get hero data from componentPositions table
    const heroData = await db.select().from(componentPositions).where(
      and(
        eq(componentPositions.address, address),
        eq(componentPositions.componentType, 'hero')
      )
    );

    if (!heroData.length) {
      return Response.json({
        heroContent: []
      });
    }

    // Get the hero details
    const details = heroData[0].details || {};
    const heroContent = (details as any).heroContent;

    // Process hero content to add image URLs
    const processedHeroContent = Array.isArray(heroContent) ? await Promise.all(heroContent.map(async (section, index) => {

      // If this section is an image type
      if (section.contentType === 'image') {
        try {
          // Get the image data from the componentImages table using the section index
          const sectionId = `${index}`;


          // Try to get images by section ID first
          let images = await getComponentImages(address, 'hero', sectionId);


          // If no images found and we have an imageId, try using that as fallback
          if (images.length === 0 && section.imageId) {

            const imageById = await getImageById(section.imageId);
            if (imageById) {
              images = [imageById];

            }
          }

          if (images.length > 0) {
            // Create a data URL from the image data
            const mimeType = section.mimeType;
            const dataUrl = `data:${mimeType};base64,${images[0].imageData}`;


            return {
              ...section,
              imageUrl: dataUrl
            };
          } else {

            // If this is an image type but no image was found, we should still return the section
            // but make sure it doesn't have an undefined imageUrl which could cause rendering issues
            return {
              ...section,
              imageUrl: section.imageUrl
            };
          }
        } catch (error) {
          console.error(`Error retrieving image for hero section ${index}:`, error);
          // Return the section with a null imageUrl to prevent rendering issues
          return {
            ...section,
            imageUrl: null
          };
        }
      } else {
        // Section is not an image type
      }
      return section;
    })) : [];
    // Processing complete

    // Return the processed hero content
    return Response.json({
      heroContent: processedHeroContent
    }, {
      headers: {
        'Cache-Control': 'no-store, no-cache, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        // Setting Cross-Origin-Opener-Policy to unsafe-none to be consistent with next.config.js
        'Cross-Origin-Opener-Policy': 'unsafe-none',
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    return handleApiError(error);
  }
}

// POST endpoint to update hero content
export async function POST(
  request: NextRequest,
  context: Context
): Promise<Response> {
  const params = await context.params;
  if (!params?.address) {
    return Response.json(
      { error: 'Address is required' },
      { status: 400 }
    );
  }

  try {
    const data = await request.json();
    const address = params.address;
    let heroContent = data.heroContent;



    // Validate hero content
    if (!Array.isArray(heroContent)) {
      return Response.json(
        { error: 'Hero content must be an array' },
        { status: 400 }
      );
    }

    // Process hero content to extract images and store them separately
    const processedHeroContent = await Promise.all(heroContent.map(async (section, index) => {
      // If this section has an image
      if (section.contentType === 'image' && section.imageUrl) {
        try {
          // Extract the base64 data and MIME type from the data URL
          const mimeType = getMimeTypeFromDataUrl(section.imageUrl);
          const base64Data = section.imageUrl.split(',')[1];

          // Generate a unique section ID (using index as a simple approach)
          const sectionId = `${index}`;

          // Delete any existing image for this section
          await deleteComponentImages(address, 'hero', sectionId);

          // Save the image to the componentImages table
          const imageId = await saveImage(address, 'hero', sectionId, base64Data);

          // Return the section with the image ID instead of the image URL
          return {
            ...section,
            imageId,
            mimeType,
            // Remove the imageUrl to save space
            imageUrl: undefined
          };
        } catch (error) {
          console.error('Error processing image for hero section:', error);
          // If there's an error, keep the section as is
          return section;
        }
      }
      return section;
    }));

    // Check if we already have a record for this address
    const existingRecord = await db.select().from(componentPositions).where(
      and(
        eq(componentPositions.address, address),
        eq(componentPositions.componentType, 'hero')
      )
    );

    // Get background color and font color from the request
    const backgroundColor = data.backgroundColor;
    const fontColor = data.fontColor;

    if (existingRecord.length > 0) {

      // Get existing details
      const existingDetails = existingRecord[0].details || {} as any;

      // Get chain from existing record
      const chain = existingRecord[0].chain;

      // Get component defaults from system settings
      const componentDefaults = await getComponentDefaults(chain);
      const heroDefaults = componentDefaults.find(comp => comp.componentType === 'hero');

      // Get default values from system settings
      const defaultDetails = heroDefaults?.details;

      // Update existing record using values from request or defaults from system settings
      await db.update(componentPositions)
        .set({
          details: {
            ...existingDetails,
            heroContent: processedHeroContent,
            backgroundColor: backgroundColor,
            fontColor: fontColor
          },
          updatedAt: new Date()
        })
        .where(
          and(
            eq(componentPositions.address, address),
            eq(componentPositions.componentType, 'hero')
          )
        );
    } else {

      // Get chain from an existing component
      const existingComponent = await db.select().from(componentPositions).where(
        eq(componentPositions.address, address)
      ).limit(1);

      // Get chain from existing component or require it to be provided
      if (existingComponent.length === 0) {
        return Response.json(
          { error: 'No existing profile found. Please ensure your profile exists first.' },
          { status: 400 }
        );
      }
      const chain = existingComponent[0].chain;

      // Get component defaults from system settings
      const componentDefaults = await getComponentDefaults(chain);
      const heroDefaults = componentDefaults.find(comp => comp.componentType === 'hero');

      // Get default values from system settings
      const defaultDetails = heroDefaults?.details;

      // Insert new record using values from request or defaults from system settings
      await db.insert(componentPositions).values({
        address: address,
        chain: chain,
        componentType: 'hero',
        order: heroDefaults?.order || '5', // Default order if not provided
        hidden: heroDefaults?.hidden || 'N', // Default to not hidden
        details: {
          heroContent: processedHeroContent,
          backgroundColor: backgroundColor,
          fontColor: fontColor
        },
        createdAt: new Date(),
        updatedAt: new Date()
      });
    }

    // Fetch the updated record to confirm it was saved correctly
    const updatedRecord = await db.select().from(componentPositions).where(
      and(
        eq(componentPositions.address, address),
        eq(componentPositions.componentType, 'hero')
      )
    );



    // For the response, we need to include the image URLs
    const responseHeroContent = await Promise.all(processedHeroContent.map(async (section, index) => {

      // If this section is an image type
      if (section.contentType === 'image') {
        try {
          // Get the image data from the componentImages table using the section index
          const sectionId = `${index}`;


          // Try to get images by section ID first
          let images = await getComponentImages(address, 'hero', sectionId);


          // If no images found and we have an imageId, try using that as fallback
          if (images.length === 0 && section.imageId) {

            const imageById = await getImageById(section.imageId);
            if (imageById) {
              images = [imageById];

            }
          }

          if (images.length > 0) {
            // Create a data URL from the image data
            const mimeType = section.mimeType;
            const dataUrl = `data:${mimeType};base64,${images[0].imageData}`;


            return {
              ...section,
              imageUrl: dataUrl
            };
          } else {

            // If this is an image type but no image was found, we should still return the section
            // but make sure it doesn't have an undefined imageUrl which could cause rendering issues
            return {
              ...section,
              imageUrl: section.imageUrl
            };
          }
        } catch (error) {
          console.error(`Error retrieving image for hero section ${index}:`, error);
          // Return the section with a null imageUrl to prevent rendering issues
          return {
            ...section,
            imageUrl: null
          };
        }
      } else {

      }
      return section;
    }));
    return Response.json({
      success: true,
      heroContent: responseHeroContent,
      savedContent: updatedRecord.length > 0 && updatedRecord[0].details ? (updatedRecord[0].details as any).heroContent : undefined,
      message: 'Hero content saved successfully'
    }, {
      headers: {
        'Cache-Control': 'no-store, no-cache, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        // Setting Cross-Origin-Opener-Policy to unsafe-none to be consistent with next.config.js
        'Cross-Origin-Opener-Policy': 'unsafe-none',
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    return handleApiError(error);
  }
}

// DELETE endpoint to delete hero content
export async function DELETE(
  _request: NextRequest,
  context: Context
): Promise<Response> {
  const params = await context.params;
  if (!params?.address) {
    return Response.json(
      { error: 'Address is required' },
      { status: 400 }
    );
  }

  const address = params.address;

  try {
    // Delete all hero images
    await deleteComponentImages(address, 'hero');

    // Delete the hero component
    await db.delete(componentPositions).where(
      and(
        eq(componentPositions.address, address),
        eq(componentPositions.componentType, 'hero')
      )
    );

    return Response.json({
      success: true,
      message: 'Hero content deleted successfully'
    });
  } catch (error) {
    console.error('Failed to delete hero content:', error);
    return handleApiError(error);
  }
}
