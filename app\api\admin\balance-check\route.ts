import { NextRequest } from 'next/server';
import { db } from '@/db/drizzle';
import { web3Profile, balanceCheckLogs } from '@/db/schema';
import { eq } from 'drizzle-orm';
import { randomUUID } from 'crypto';
import { getTokenRequirements } from '@/app/utils/systemSettings';
import { checkWalletBalanceWithRequirements, hasAnyTokenRequirements } from '@/lib/tokenValidation';

export async function POST(request: NextRequest): Promise<Response> {
  const jobId = randomUUID();
  let totalChecked = 0;
  let totalStatusChanged = 0;
  let totalErrors = 0;
  const statusChangedProfiles: Array<{
    address: string;
    name: string;
    chain: string;
    oldStatus: string;
    newStatus: string;
    balanceCheckResult: any;
    tokenRequirements: any;
  }> = [];

  try {
    console.log(`[Balance Check Job ${jobId}] Starting bulk balance check for all approved profiles`);

    // Get all approved profiles
    const approvedProfiles = await db
      .select()
      .from(web3Profile)
      .where(eq(web3Profile.status, 'approved'));

    console.log(`[Balance Check Job ${jobId}] Found ${approvedProfiles.length} approved profiles to check`);

    if (approvedProfiles.length === 0) {
      return Response.json({
        success: true,
        jobId,
        message: 'No approved profiles found to check',
        summary: {
          totalChecked: 0,
          totalStatusChanged: 0,
          totalErrors: 0
        }
      });
    }

    // Process each profile
    for (const profile of approvedProfiles) {
      totalChecked++;
      let statusChanged = false;
      let errorMessage: string | undefined;
      let balanceCheckResult: any = null;
      let tokenRequirements: any = null;

      try {
        console.log(`[Balance Check Job ${jobId}] Checking profile ${profile.address} on chain ${profile.chain}`);

        // Get token requirements for this chain
        tokenRequirements = await getTokenRequirements(profile.chain);
        
        // Check if token requirements are configured
        if (!hasAnyTokenRequirements(undefined, tokenRequirements)) {
          console.log(`[Balance Check Job ${jobId}] No token requirements configured for chain ${profile.chain}, skipping profile ${profile.address}`);
          
          // Log the skip
          await db.insert(balanceCheckLogs).values({
            jobId,
            profileAddress: profile.address,
            chainId: profile.chain,
            oldStatus: profile.status,
            newStatus: profile.status,
            balanceCheckResult: { skipped: true, reason: 'No token requirements configured' },
            tokenRequirements,
            statusChanged: 'N',
            errorMessage: 'No token requirements configured for this chain'
          });
          
          continue;
        }

        // Check wallet balance
        balanceCheckResult = await checkWalletBalanceWithRequirements(
          profile.address,
          tokenRequirements,
          profile.chain
        );

        console.log(`[Balance Check Job ${jobId}] Balance check result for ${profile.address}:`, {
          canProceed: balanceCheckResult.canProceed,
          validationPath: balanceCheckResult.validationPath
        });

        // If balance check fails, update status from approved to new
        if (!balanceCheckResult.canProceed) {
          console.log(`[Balance Check Job ${jobId}] Profile ${profile.address} no longer meets token requirements, updating status to 'new'`);

          // Update profile status
          await db
            .update(web3Profile)
            .set({
              status: 'new',
              updatedAt: new Date()
            })
            .where(eq(web3Profile.address, profile.address));

          statusChanged = true;
          totalStatusChanged++;

          // Add to status changed profiles list
          statusChangedProfiles.push({
            address: profile.address,
            name: profile.name || 'Unnamed Profile',
            chain: profile.chain,
            oldStatus: profile.status,
            newStatus: 'new',
            balanceCheckResult,
            tokenRequirements
          });
        }

      } catch (error) {
        totalErrors++;
        errorMessage = error instanceof Error ? error.message : 'Unknown error during balance check';
      }

      // Log the operation
      await db.insert(balanceCheckLogs).values({
        jobId,
        profileAddress: profile.address,
        chainId: profile.chain,
        oldStatus: profile.status,
        newStatus: statusChanged ? 'new' : profile.status,
        balanceCheckResult,
        tokenRequirements,
        statusChanged: statusChanged ? 'Y' : 'N',
        errorMessage
      });
    }

    const summary = {
      totalChecked,
      totalStatusChanged,
      totalErrors,
      statusChangedProfiles
    };

    return Response.json({
      success: true,
      jobId,
      message: `Balance check completed. Checked ${totalChecked} profiles, ${totalStatusChanged} status changes, ${totalErrors} errors.`,
      summary
    });

  } catch (error) {

    
    return Response.json({
      success: false,
      jobId,
      error: error instanceof Error ? error.message : 'Unknown error during bulk balance check',
      summary: {
        totalChecked,
        totalStatusChanged,
        totalErrors: totalErrors + 1
      }
    }, { status: 500 });
  }
}

// GET endpoint to retrieve balance check logs
export async function GET(request: NextRequest): Promise<Response> {
  try {
    const { searchParams } = new URL(request.url);
    const jobId = searchParams.get('jobId');
    const limit = parseInt(searchParams.get('limit') || '100');
    const offset = parseInt(searchParams.get('offset') || '0');

    let logs;

    if (jobId) {
      logs = await db
        .select()
        .from(balanceCheckLogs)
        .where(eq(balanceCheckLogs.jobId, jobId))
        .limit(limit)
        .offset(offset)
        .orderBy(balanceCheckLogs.createdAt);
    } else {
      logs = await db
        .select()
        .from(balanceCheckLogs)
        .limit(limit)
        .offset(offset)
        .orderBy(balanceCheckLogs.createdAt);
    }

    return Response.json({
      success: true,
      logs,
      pagination: {
        limit,
        offset,
        total: logs.length
      }
    });

  } catch (error) {
    console.error('Error fetching balance check logs:', error);
    return Response.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch balance check logs'
    }, { status: 500 });
  }
}
