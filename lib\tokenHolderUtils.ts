/**
 * Token holder utilities for processing CSV data and database operations
 */

import { db } from '@/db/drizzle';
import { tokenHolderSnapshots, tokenHolders, tokenHolderJobs } from '@/db/schema';
import { eq, and, desc } from 'drizzle-orm';

export interface TokenHolderRecord {
  HolderAddress: string;
  Balance: string;
  PendingBalanceUpdate: 'Yes' | 'No';
}

export interface TokenHolderSnapshot {
  id: number;
  contractAddress: string;
  chainId: string;
  tokenName: string | null;
  snapshotDate: Date;
  totalHolders: number;
  createdAt: Date | null;
  updatedAt: Date | null;
}

export interface TokenHolderData {
  holderAddress: string;
  balance: string;
  balanceNumeric: string | null;
  pendingBalanceUpdate: string | null;
}

/**
 * Parse CSV content from blockchain explorer exports (CronoScan, Solscan, etc.)
 */
export function parseTokenHolderCSV(csvContent: string, chainId?: string): TokenHolderRecord[] {
  const lines = csvContent.trim().split('\n');

  // Log the first few lines to understand the format
  console.log('CSV parsing - first 3 lines:');
  lines.slice(0, 3).forEach((line, i) => {
    console.log(`Line ${i}: ${line}`);
  });

  const headers = lines[0].split(',').map(h => h.replace(/"/g, '').trim().toLowerCase());
  console.log('Headers found:', headers);

  // Find column indices (support different naming conventions for different chains)
  const addressIndex = headers.findIndex(h =>
    h.includes('address') || h.includes('holder') || h.includes('account') || h.includes('wallet')
  );
  const balanceIndex = headers.findIndex(h =>
    (h.includes('balance') || h.includes('amount') || h.includes('quantity') || h.includes('tokens')) &&
    !h.includes('pending') && !h.includes('update')
  );
  const pendingIndex = headers.findIndex(h =>
    h.includes('pending') || h.includes('update')
  );

  console.log(`Column indices for chainId ${chainId || 'unknown'} - Address: ${addressIndex}, Balance: ${balanceIndex}, Pending: ${pendingIndex}`);

  const records: TokenHolderRecord[] = [];

  for (let i = 1; i < lines.length; i++) {
    const line = lines[i].trim();
    if (!line) continue; // Skip empty lines

    // Handle CSV parsing more carefully to deal with commas in quoted values
    const values = [];
    let current = '';
    let inQuotes = false;

    for (let j = 0; j < line.length; j++) {
      const char = line[j];

      if (char === '"') {
        inQuotes = !inQuotes;
      } else if (char === ',' && !inQuotes) {
        values.push(current.trim());
        current = '';
      } else {
        current += char;
      }
    }
    values.push(current.trim()); // Add the last value

    // Use the detected column indices or fall back to positional
    let holderAddress, balance, pendingUpdate;

    if (addressIndex >= 0 && balanceIndex >= 0) {
      holderAddress = values[addressIndex]?.replace(/"/g, '').trim();
      balance = values[balanceIndex]?.replace(/"/g, '').trim();
      pendingUpdate = pendingIndex >= 0 ? values[pendingIndex]?.replace(/"/g, '').trim() : 'No';
    } else if (values.length >= 2) {
      // Fallback to positional parsing
      holderAddress = values[0]?.replace(/"/g, '').trim();
      balance = values[1]?.replace(/"/g, '').trim();
      pendingUpdate = values.length >= 3 ? values[2]?.replace(/"/g, '').trim() : 'No';
    } else {
      continue; // Skip invalid rows
    }

    // Special handling for CronoScan format which might have different column structure
    // If the "balance" looks like a decimal number with many digits, it's probably the actual balance
    // If it's a short number, it might be misaligned
    if (balance && balance.includes('.') && balance.length > 10) {
      // This looks like a proper balance with decimals
    } else if (values.length > 2 && values[2] && values[2].includes('.') && values[2].length > 10) {
      // The balance might be in the third column
      pendingUpdate = balance || 'No';
      balance = values[2]?.replace(/"/g, '').trim();
    }

    // Normalize pending balance update values
    if (pendingUpdate && (pendingUpdate.toLowerCase() === 'yes' || pendingUpdate === '1' || pendingUpdate === 'true')) {
      pendingUpdate = 'Yes';
    } else {
      pendingUpdate = 'No';
    }

    // Validate address format (supports EVM chains and Solana addresses)
    const isValidAddress = holderAddress && balance && (
      // EVM-style address (0x + 40 hex chars) - works for Ethereum, Cronos, Polygon, BSC, Arbitrum, etc.
      (holderAddress.startsWith('0x') && holderAddress.length === 42 && /^0x[a-fA-F0-9]{40}$/.test(holderAddress)) ||
      // Solana-style address (base58, typically 32-44 chars)
      (/^[1-9A-HJ-NP-Za-km-z]{32,44}$/.test(holderAddress) && holderAddress.length >= 32 && holderAddress.length <= 44) ||
      // Other blockchain address formats (flexible validation for future chains)
      (holderAddress.length >= 20 && holderAddress.length <= 64 && /^[a-zA-Z0-9]+$/.test(holderAddress))
    );

    if (isValidAddress) {
      records.push({
        HolderAddress: holderAddress,
        Balance: balance,
        PendingBalanceUpdate: pendingUpdate as 'Yes' | 'No'
      });
    }
  }


  return records;
}

/**
 * Convert balance string to numeric value
 * Handles commas and large numbers
 * Returns string for decimal field compatibility
 */
export function parseBalance(balanceString: string): string {
  // Remove commas and return as string for decimal field
  const cleanBalance = balanceString.replace(/,/g, '');
  return cleanBalance;
}

/**
 * Create a new token holder snapshot
 */
export async function createTokenHolderSnapshot(
  contractAddress: string,
  chainId: string,
  tokenName: string,
  holders: TokenHolderRecord[]
): Promise<number> {
  try {
    // Create snapshot record
    const snapshot = await db.insert(tokenHolderSnapshots).values({
      contractAddress,
      chainId,
      tokenName,
      snapshotDate: new Date(), // Today's date as Date object
      totalHolders: holders.length,
    });

    const snapshotId = snapshot[0].insertId;

    // Insert all holder records
    const holderData = holders.map(holder => ({
      snapshotId: snapshotId,
      holderAddress: holder.HolderAddress,
      balance: holder.Balance,
      balanceNumeric: parseBalance(holder.Balance),
      pendingBalanceUpdate: holder.PendingBalanceUpdate,
    }));

    // Insert in batches to avoid memory issues
    const batchSize = 1000;
    for (let i = 0; i < holderData.length; i += batchSize) {
      const batch = holderData.slice(i, i + batchSize);
      await db.insert(tokenHolders).values(batch);
    }

    return snapshotId;
  } catch (error) {
    console.error('Error creating token holder snapshot:', error);
    throw error;
  }
}

/**
 * Get latest snapshot for a token
 */
export async function getLatestSnapshot(
  contractAddress: string,
  chainId: string
): Promise<TokenHolderSnapshot | null> {
  try {
    const snapshots = await db
      .select()
      .from(tokenHolderSnapshots)
      .where(
        and(
          eq(tokenHolderSnapshots.contractAddress, contractAddress),
          eq(tokenHolderSnapshots.chainId, chainId)
        )
      )
      .orderBy(desc(tokenHolderSnapshots.createdAt))
      .limit(1);

    return snapshots.length > 0 ? snapshots[0] : null;
  } catch (error) {
    console.error('Error getting latest snapshot:', error);
    return null;
  }
}

/**
 * Get holders for a specific snapshot
 */
export async function getSnapshotHolders(snapshotId: number): Promise<TokenHolderData[]> {
  try {
    const holders = await db
      .select({
        holderAddress: tokenHolders.holderAddress,
        balance: tokenHolders.balance,
        balanceNumeric: tokenHolders.balanceNumeric,
        pendingBalanceUpdate: tokenHolders.pendingBalanceUpdate,
      })
      .from(tokenHolders)
      .where(eq(tokenHolders.snapshotId, snapshotId))
      .orderBy(desc(tokenHolders.balanceNumeric));

    return holders;
  } catch (error) {
    console.error('Error getting snapshot holders:', error);
    return [];
  }
}

/**
 * Create a new token holder job
 */
export async function createTokenHolderJob(
  contractAddress: string,
  chainId: string,
  tokenName?: string
): Promise<number> {
  try {
    const job = await db.insert(tokenHolderJobs).values({
      contractAddress,
      chainId,
      tokenName,
      status: 'pending',
      startedAt: new Date(),
    });

    return job[0].insertId;
  } catch (error) {
    console.error('Error creating token holder job:', error);
    throw error;
  }
}

/**
 * Update token holder job status
 */
export async function updateTokenHolderJob(
  jobId: number,
  status: 'processing' | 'completed' | 'failed',
  totalHolders?: number,
  errorMessage?: string
): Promise<void> {
  try {
    const updateData: any = {
      status,
      updatedAt: new Date(),
    };

    if (status === 'completed') {
      updateData.completedAt = new Date();
      if (totalHolders !== undefined) {
        updateData.totalHolders = totalHolders;
      }
    }

    if (status === 'failed' && errorMessage) {
      updateData.errorMessage = errorMessage;
    }

    await db
      .update(tokenHolderJobs)
      .set(updateData)
      .where(eq(tokenHolderJobs.id, jobId));
  } catch (error) {
    console.error('Error updating token holder job:', error);
    throw error;
  }
}

/**
 * Get all snapshots for a token (for history)
 */
export async function getTokenSnapshots(
  contractAddress: string,
  chainId: string
): Promise<TokenHolderSnapshot[]> {
  try {
    const snapshots = await db
      .select()
      .from(tokenHolderSnapshots)
      .where(
        and(
          eq(tokenHolderSnapshots.contractAddress, contractAddress),
          eq(tokenHolderSnapshots.chainId, chainId)
        )
      )
      .orderBy(desc(tokenHolderSnapshots.createdAt));

    return snapshots;
  } catch (error) {
    console.error('Error getting token snapshots:', error);
    return [];
  }
}
