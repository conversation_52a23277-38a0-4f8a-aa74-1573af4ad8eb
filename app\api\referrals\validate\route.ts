import { NextRequest } from 'next/server';
import { validateReferralCode } from '@/app/utils/referralUtils';
import { handleApiError } from '@/app/api/errorHandler';

export async function GET(request: NextRequest): Promise<Response> {
  try {
    const { searchParams } = new URL(request.url);
    const referralCode = searchParams.get('code');

    if (!referralCode) {
      return Response.json(
        { error: 'Referral code is required' },
        { status: 400 }
      );
    }

    const validation = await validateReferralCode(referralCode);

    return Response.json({
      isValid: validation.isValid,
      referrerAddress: validation.referrerAddress
    });
  } catch (error) {
    return handleApiError(error);
  }
}
