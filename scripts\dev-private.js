#!/usr/bin/env node

/**
 * Development server wrapper that filters sensitive data from logs
 */

const { spawn } = require('child_process');
const { Transform } = require('stream');

// Function to check if a line contains wallet addresses
function containsWalletAddress(line) {
  if (typeof line !== 'string') return false;

  // Check for Ethereum addresses (0x followed by 40 hex characters)
  const ethRegex = /0x[a-fA-F0-9]{40}/;
  // Check for Solana addresses (Base58 encoded, typically 32-44 characters)
  const solRegex = /[1-9A-HJ-NP-Za-km-z]{32,44}/;

  return ethRegex.test(line) || solRegex.test(line);
}

// Function to sanitize log lines
function sanitizeLine(line) {
  if (typeof line !== 'string') return line;

  // Always replace Ethereum addresses
  line = line.replace(/0x[a-fA-F0-9]{40}/g, '[WALLET_ADDRESS]');

  // Replace Solana addresses in any context that looks like an address
  line = line.replace(/[1-9A-HJ-NP-Za-km-z]{32,44}/g, (match) => {
    // More aggressive replacement for addresses in logs
    const context = line.toLowerCase();
    if (context.includes('address') || context.includes('search') ||
        context.includes('pathname') || context.includes('api/') ||
        context.includes('?') || context.includes('=')) {
      return '[WALLET_ADDRESS]';
    }
    return match;
  });

  return line;
}

// Create transform streams to filter output
const createFilterStream = () => {
  return new Transform({
    transform(chunk, encoding, callback) {
      let output = chunk.toString();

      // Always sanitize the entire output
      output = sanitizeLine(output);

      // Debug: Log that we're filtering (remove this after testing)
      if (containsWalletAddress(chunk.toString())) {
        console.error('🔒 [FILTERED] Wallet address detected and replaced');
      }

      callback(null, output);
    }
  });
};

// Set environment variables for reduced logging
const env = {
  ...process.env,
  NEXT_TELEMETRY_DISABLED: '1',
  NEXT_PRIVATE_DEBUG_CACHE: 'false',
  NEXT_PRIVATE_DEBUG_ROUTE: 'false',
  NEXT_PRIVATE_DEBUG_REVALIDATE: 'false',
};

console.log('🔒 Starting Next.js development server with privacy protection...');
console.log('🔒 Wallet addresses will be replaced with [WALLET_ADDRESS]');
console.log('🔒 Starting server...\n');

// Start Next.js development server
const nextProcess = spawn('npx', ['next', 'dev', '--turbopack'], {
  env,
  cwd: process.cwd(),
});

// Filter stdout
const stdoutFilter = createFilterStream();
nextProcess.stdout.pipe(stdoutFilter).pipe(process.stdout);

// Filter stderr
const stderrFilter = createFilterStream();
nextProcess.stderr.pipe(stderrFilter).pipe(process.stderr);

// Handle process events
nextProcess.on('close', (code) => {
  console.log(`\n🔒 Development server exited with code ${code}`);
  process.exit(code);
});

nextProcess.on('error', (error) => {
  console.error('🔒 Failed to start development server:', error);
  process.exit(1);
});

// Handle Ctrl+C gracefully
process.on('SIGINT', () => {
  console.log('\n🔒 Shutting down development server...');
  nextProcess.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('\n🔒 Shutting down development server...');
  nextProcess.kill('SIGTERM');
});
