'use client';

import { useState, useEffect } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { Loader2 } from 'lucide-react';
import { useAppKitAccount, useAppKitNetwork } from '@reown/appkit/react';
// Old render components removed
import RenderHero from '@/app/components/renders/render_hero';
import RenderSocialLinks from '@/app/components/renders/render_sociallinks';
import RenderBannerPfp from '@/app/components/renders/render_bannerpfp';
import { useMetadata } from '@/app/contexts/MetadataContext';
import { checkProfileStatus } from '@/lib/profileStatus';
import {
  checkWalletBalanceWithRequirements,
  hasAnyTokenRequirements,
  FullTokenRequirements
} from '@/lib/tokenValidation';

interface ProfileData {
  address: string;
  name?: string; // Optional for backward compatibility
  profileName?: string; // New field
  bio?: string; // Optional for backward compatibility
  profileBio?: string; // New field
  chain: string;
  // socialLinks removed - now stored in componentPositions table for socialLinks component
  components: {
    componentType: string;
    order: string;
    hidden: string;
    backgroundColor?: string;
    profileName?: string;
    profileBio?: string;
    details?: any;
  }[];
  compPosition?: 'left' | 'center' | 'right';
}

export default function ClientPage({ nameOrAddress }: { nameOrAddress: string }) {
  const pathname = usePathname();
  const router = useRouter();
  const { address, isConnected } = useAppKitAccount();
  const { chainId } = useAppKitNetwork();
  const [profileData, setProfileData] = useState<ProfileData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { fetchBannerMetadata, fetchProfilePictureMetadata, fetchBannerPfpMetadata } = useMetadata();
  const [bannerMetadata, setBannerMetadata] = useState<any>(null);
  const [profilePictureMetadata, setProfilePictureMetadata] = useState<any>(null);
  const [bannerPfpMetadata, setBannerPfpMetadata] = useState<any>(null);

  // Real-time balance validation state
  const [tokenRequirements, setTokenRequirements] = useState<FullTokenRequirements | null>(null);
  const [balanceValidation, setBalanceValidation] = useState<{
    isValid: boolean;
    isChecking: boolean;
    error?: string;
  }>({ isValid: true, isChecking: false });

  // Fetch token requirements for current chain
  useEffect(() => {
    async function fetchTokenRequirements() {
      if (!chainId) return;

      try {
        // Add cache busting to ensure fresh data
        const cacheBuster = Date.now();
        const response = await fetch(`/api/token-requirements/${chainId}?full=true&_=${cacheBuster}`);
        if (response.ok) {
          const requirements = await response.json();
          setTokenRequirements(requirements);
        }
      } catch (error) {
        console.error('Error fetching token requirements:', error);
      }
    }

    fetchTokenRequirements();
  }, [chainId]);

  // Real-time balance validation for current user
  useEffect(() => {
    async function validateCurrentUserBalance() {
      // Only validate if user is connected and we have token requirements
      if (!isConnected || !address || !chainId || !tokenRequirements) {
        setBalanceValidation({ isValid: true, isChecking: false });
        return;
      }

      // Skip validation if no token requirements are configured
      if (!hasAnyTokenRequirements(undefined, tokenRequirements)) {
        setBalanceValidation({ isValid: true, isChecking: false });
        return;
      }

      try {
        setBalanceValidation({ isValid: true, isChecking: true });

        const balanceResult = await checkWalletBalanceWithRequirements(
          address,
          tokenRequirements,
          chainId.toString()
        );

        // If user doesn't meet requirements, redirect to profile-error
        if (!balanceResult.canProceed) {
          window.location.href = `/profile-error?status=insufficient-balance&name=${nameOrAddress}`;
          return;
        }

        setBalanceValidation({ isValid: true, isChecking: false });
      } catch (error) {
        console.error('Error validating user balance:', error);
        setBalanceValidation({
          isValid: false,
          isChecking: false,
          error: 'Failed to validate wallet balance'
        });
      }
    }

    validateCurrentUserBalance();
  }, [isConnected, address, chainId, tokenRequirements, nameOrAddress]);

  // Check profile status and validate access before loading profile
  useEffect(() => {
    async function validateAndFetchProfile() {
      try {
        setLoading(true);
        setError(null);

        // Step 1: Check profile status first
        const statusResult = await checkProfileStatus(nameOrAddress);

        // If profile is not approved, redirect to error page
        if (!statusResult.isApproved && statusResult.status !== 'not-found') {
          const nameParam = statusResult.name ? `&name=${statusResult.name}` : `&name=${nameOrAddress}`;
          window.location.href = `/profile-error?status=${statusResult.status}${nameParam}`;
          return;
        }

        // Step 2: Fetch profile data only after status validation
        const response = await fetch(`/api/profile/${nameOrAddress}`);

        if (!response.ok) {
          const errorResponse = await response.json();

          if (response.status === 404) {
            if (errorResponse.error === 'No profiles exist in the database yet') {
              setError('No profiles have been created yet. Be the first to create one!');
            } else if (errorResponse.error === 'Profile components not properly initialized') {
              setError('Profile system is not properly initialized. Please contact support.');
            } else {
              setError(`Profile "${nameOrAddress}" not found`);
            }
            setLoading(false);
            return;
          }

          console.error('Failed to fetch profile:', errorResponse);
          throw new Error(errorResponse.error || 'Failed to load profile');
        }

        const data = await response.json();
        setProfileData(data);

        // Fetch bannerpfp metadata
        if (data.address) {
          try {
            // Fetch bannerpfp metadata first
            const bannerPfpMeta = await fetchBannerPfpMetadata(data.address);
            if (bannerPfpMeta) {
              setBannerPfpMetadata(bannerPfpMeta);

              // Now fetch banner and profile picture metadata which will use the bannerpfp data
              const bannerMeta = await fetchBannerMetadata(data.address);
              if (bannerMeta) {
                setBannerMetadata(bannerMeta);
              }

              const profilePicMeta = await fetchProfilePictureMetadata(data.address);
              if (profilePicMeta) {
                setProfilePictureMetadata(profilePicMeta);
              }
            }
          } catch (metadataError) {
            console.error('Error fetching component metadata:', metadataError);
            // Continue showing the profile even if metadata fetch fails
          }
        }
      } catch (err: any) {
        console.error('Error loading profile:', err);
        setError(err.message || 'An error occurred while loading the profile');
      } finally {
        setLoading(false);
      }
    }

    validateAndFetchProfile();
  }, [nameOrAddress, fetchBannerMetadata, fetchProfilePictureMetadata, address]);

  return (
    <main className="relative min-h-screen flex flex-col items-center">
      <div className="w-full max-w-4xl mx-auto px-4 sm:px-6 pt-24 pb-8 z-10 relative">
        {loading || balanceValidation.isChecking ? (
          <div className="flex justify-center items-center min-h-[300px]">
            <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
            <span className="ml-2 text-neutral-300">
              {balanceValidation.isChecking ? 'Validating wallet balance...' : 'Loading profile...'}
            </span>
          </div>
        ) : error ? (
          <div className="bg-red-900/20 border border-red-900/50 rounded-lg p-6 text-center">
            <h3 className="text-red-400 font-medium mb-2">Error</h3>
            <p className="text-neutral-300 mb-4">{error}</p>
            <button
              onClick={() => window.location.href = '/profiles'}
              className="px-4 py-2 bg-red-900/50 hover:bg-red-800/50 text-white rounded-md transition-colors"
            >
              Back to Profiles
            </button>
          </div>
        ) : profileData ? (
          <div className="space-y-0 border border-neutral-700 overflow-hidden">
            {profileData.components
              .filter(c => c.hidden !== 'Y')
              .sort((a, b) => parseInt(a.order) - parseInt(b.order))
              .map((component, index) => {
                // Skip old component types
                if (component.componentType === 'banner' || component.componentType === 'profilePicture') {
                  return null;
                } else if (component.componentType === 'details') {
                  // Skip rendering deprecated 'details' component
                  return null;
                } else if (component.componentType === 'hero') {
                  return (
                    <div key={`hero-${component.order}`} className="border-b border-neutral-700">
                      <RenderHero
                        address={profileData.address}
                        componentData={{
                          ...component,
                          address: profileData.address,
                          chain: profileData.chain
                        }}
                        showPositionLabel={false}
                      />
                    </div>
                  );
                } else if (component.componentType === 'socialLinks') {
                  return (
                    <div key={`socialLinks-${component.order}`} className="border-b border-neutral-700">
                      <RenderSocialLinks
                        profileData={{
                          address: profileData.address,
                          chain: profileData.chain,
                          name: profileData.name || '',
                          bio: ''
                        }}
                        componentData={{
                          ...component,
                          address: profileData.address,
                          chain: profileData.chain
                        }}
                        showPositionLabel={false}
                      />
                    </div>
                  );
                } else if (component.componentType === 'bannerpfp' && bannerPfpMetadata) {
                  return (
                    <div key={`bannerpfp-${component.order}`} className="border-b border-neutral-700">
                      <RenderBannerPfp
                        address={profileData.address}
                        componentData={{
                          ...component,
                          address: profileData.address,
                          chain: profileData.chain
                        }}
                        showPositionLabel={false}
                        profileName={profileData.name || ''}
                        profileBio={''}
                      />
                    </div>
                  );
                }
                return null;
              })}
          </div>
        ) : (
          <div className="bg-yellow-900/20 border border-yellow-900/50 rounded-lg p-6 text-center">
            <h3 className="text-yellow-400 font-medium mb-2">Profile Not Found</h3>
            <p className="text-neutral-300 mb-4">
              The profile "{nameOrAddress}" doesn't exist or has been removed.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <button
                onClick={() => window.location.href = '/profiles'}
                className="px-4 py-2 bg-yellow-900/50 hover:bg-yellow-800/50 text-white rounded-md transition-colors"
              >
                Browse Profiles
              </button>
              <button
                onClick={() => window.location.href = '/createpage'}
                className="px-4 py-2 bg-blue-900/50 hover:bg-blue-800/50 text-white rounded-md transition-colors"
              >
                Create Your Profile
              </button>
            </div>
          </div>
        )}
      </div>
    </main>
  );
}
