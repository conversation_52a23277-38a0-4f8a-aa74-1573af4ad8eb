import { NextRequest } from 'next/server';
import { db } from '@/db/drizzle';
import { systemSettings, web3Profile } from '@/db/schema';
import { eq } from 'drizzle-orm';

// GET all system settings
export async function GET(request: NextRequest): Promise<Response> {
  try {
    // Get all settings
    const settings = await db.select().from(systemSettings);
    
    return Response.json(settings, {
      headers: {
        'Cache-Control': 'no-store, no-cache, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
  } catch (error) {
    return Response.json(
      { error: 'Failed to fetch settings' },
      { status: 500 }
    );
  }
}

// POST to update system settings
export async function POST(request: NextRequest): Promise<Response> {
  try {
    const data = await request.json();
    const { id, value, address } = data;

    if (!id || !value) {
      return Response.json(
        { error: 'Setting ID and value are required' },
        { status: 400 }
      );
    }

    // Check if user is admin
    if (address) {
      const user = await db
        .select()
        .from(web3Profile)
        .where(eq(web3Profile.address, address));

      if (user.length === 0 || user[0].role !== 'admin') {
        return Response.json(
          { error: 'Unauthorized: Only admins can update system settings' },
          { status: 403 }
        );
      }
    } else {
      return Response.json(
        { error: 'Unauthorized: Address is required' },
        { status: 403 }
      );
    }

    // Check if setting exists
    const existingSetting = await db
      .select()
      .from(systemSettings)
      .where(eq(systemSettings.id, id));

    if (existingSetting.length === 0) {
      // Create new setting
      await db.insert(systemSettings).values({
        id,
        value,
        updatedAt: new Date()
      });
    } else {
      // Update existing setting
      await db.update(systemSettings)
        .set({
          value,
          updatedAt: new Date()
        })
        .where(eq(systemSettings.id, id));
    }

    return Response.json({ success: true });
  } catch (error) {
    return Response.json(
      { error: 'Failed to update setting' },
      { status: 500 }
    );
  }
}
