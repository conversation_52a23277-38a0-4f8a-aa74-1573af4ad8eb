import { NextRequest } from 'next/server';
import { db } from '@/db/drizzle';
import { web3Profile } from '@/db/schema';
import { eq } from 'drizzle-orm';

type Context = {
  params: Promise<{
    chainId: string;
  }>;
};

// GET users for a specific chain
export async function GET(
  _request: NextRequest,
  context: Context
): Promise<Response> {
  try {
    const { chainId } = await context.params;

    if (!chainId) {
      return Response.json(
        { error: 'Chain ID is required' },
        { status: 400 }
      );
    }

    // Get all profiles for the specified chain
    const profiles = await db
      .select()
      .from(web3Profile)
      .where(eq(web3Profile.chain, chainId));

    return Response.json(profiles, {
      headers: {
        'Cache-Control': 'no-store, no-cache, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
  } catch (error) {
    return Response.json(
      { error: 'Failed to fetch users for chain' },
      { status: 500 }
    );
  }
}
