-- Migration to add sniper functionality tables
-- Run this SQL in your MySQL database

-- <PERSON><PERSON>per creators table - stores creator addresses and names for monitoring
CREATE TABLE IF NOT EXISTS `sniper_creators` (
  `id` int NOT NULL AUTO_INCREMENT,
  `address` varchar(70) NOT NULL COMMENT 'Creator wallet address',
  `name` varchar(100) NOT NULL COMMENT 'Creator name/label',
  `chain_id` varchar(40) NOT NULL COMMENT 'Chain ID',
  `is_active` varchar(1) NOT NULL DEFAULT 'Y' COMMENT 'Y/N',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `address_chain_idx` (`address`, `chain_id`),
  KEY `name_idx` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Sniper watched addresses table - stores specific addresses to watch (optional)
CREATE TABLE IF NOT EXISTS `sniper_watched_addresses` (
  `id` int NOT NULL AUTO_INCREMENT,
  `address` varchar(70) NOT NULL COMMENT 'Watched wallet address',
  `name` varchar(100) COMMENT 'Optional name/label for the address',
  `chain_id` varchar(40) NOT NULL COMMENT 'Chain ID',
  `is_active` varchar(1) NOT NULL DEFAULT 'Y' COMMENT 'Y/N',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `address_chain_idx` (`address`, `chain_id`),
  KEY `name_idx` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Sniper configurations table - stores sniper settings per user/chain
CREATE TABLE IF NOT EXISTS `sniper_configs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_address` varchar(70) NOT NULL COMMENT 'User who owns this config',
  `chain_id` varchar(40) NOT NULL COMMENT 'Chain ID',
  `private_key` varchar(200) COMMENT 'Encrypted private key',
  `buy_amount` varchar(20) NOT NULL DEFAULT '10' COMMENT 'Amount to buy with',
  `slippage` varchar(10) NOT NULL DEFAULT '5' COMMENT 'Slippage tolerance',
  `auto_buy_enabled` varchar(1) NOT NULL DEFAULT 'N' COMMENT 'Y/N',
  `selected_creators` json COMMENT 'Array of creator IDs to monitor',
  `selected_watched_addresses` json COMMENT 'Array of watched address IDs',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_chain_idx` (`user_address`, `chain_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
