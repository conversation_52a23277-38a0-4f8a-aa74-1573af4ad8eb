-- Migration to add balance check logs table
-- This table tracks bulk balance checking operations for approved profiles

CREATE TABLE IF NOT EXISTS `balance_check_logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `job_id` varchar(36) NOT NULL COMMENT 'UUID for the bulk operation',
  `profile_address` varchar(255) NOT NULL COMMENT 'Address of the profile being checked',
  `chain_id` varchar(40) NOT NULL COMMENT 'Chain ID of the profile',
  `old_status` varchar(50) NOT NULL COMMENT 'Profile status before balance check',
  `new_status` varchar(50) NOT NULL COMMENT 'Profile status after balance check',
  `balance_check_result` json COMMENT 'Full balance check result from token validation',
  `token_requirements` json COMMENT 'Token requirements used for validation',
  `status_changed` varchar(1) NOT NULL DEFAULT 'N' COMMENT 'Whether status was changed (Y/N)',
  `error_message` text COMMENT 'Error message if balance check failed',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  <PERSON>IMARY KEY (`id`),
  <PERSON><PERSON><PERSON> `job_id_idx` (`job_id`),
  <PERSON><PERSON>Y `profile_address_idx` (`profile_address`),
  KEY `created_at_idx` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
