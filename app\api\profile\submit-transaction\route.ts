import { NextRequest } from 'next/server';
import { db } from '@/db/drizzle';
import { web3Profile, profileReferrals } from '@/db/schema';
import { eq } from 'drizzle-orm';
import { validateReferralCode } from '@/app/utils/referralUtils';
import { randomUUID } from 'crypto';

export async function POST(request: NextRequest): Promise<Response> {
  try {
    const { address, transactionHash, referralCode, validationMode } = await request.json();

    if (!address) {
      return Response.json(
        { error: 'Address is required' },
        { status: 400 }
      );
    }

    // Validate based on validation mode
    if (validationMode === 'token') {
      // Token mode - no transaction hash required anymore
    } else {
      // Referral-only mode - referral code is optional
      // No validation needed - user can submit with or without referral code
    }

    // Check if profile exists
    const profile = await db
      .select()
      .from(web3Profile)
      .where(eq(web3Profile.address, address));

    if (profile.length === 0) {
      return Response.json(
        { error: 'Profile not found' },
        { status: 404 }
      );
    }

    // Get referrer address if referral code is provided (for referral record creation)
    let referrerAddress: string | undefined;
    if (referralCode) {
      const validation = await validateReferralCode(referralCode);
      referrerAddress = validation.referrerAddress; // Use if valid, undefined if not
    }

    // Prepare update data based on validation mode
    const updateData: any = {
      status: 'pending',
      updatedAt: new Date()
    };

    // Add transaction hash only in token mode
    if (validationMode === 'token' && transactionHash) {
      updateData.transactionHash = transactionHash;
    }

    // Always save referral code if provided (regardless of validation)
    if (referralCode) {
      updateData.referredBy = referralCode;
    }

    // Update profile with transaction hash and set status to pending
    await db.update(web3Profile)
      .set(updateData)
      .where(eq(web3Profile.address, address));

    // Create referral record only if referral code is valid (has referrerAddress)
    if (referralCode && referrerAddress) {
      try {
        await db.insert(profileReferrals).values({
          id: randomUUID(),
          referrerAddress,
          referredAddress: address,
          referralCode,
          createdAt: new Date(),
          updatedAt: new Date()
        });
      } catch (error) {
        console.error('Failed to create referral record:', error);
        // Don't fail the transaction submission if referral record creation fails
      }
    }

    return Response.json({ success: true });
  } catch (error) {
    console.error('Failed to submit profile information:', error);
    return Response.json(
      { error: 'Failed to submit profile information' },
      { status: 500 }
    );
  }
}
