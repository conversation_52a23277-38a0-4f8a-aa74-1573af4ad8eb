import { config } from "dotenv";
import { db } from './drizzle';
import { systemSettings } from './schema';
import { like, or } from 'drizzle-orm';

config({ path: ".env" });

/**
 * Verify that default settings were created for all chains
 */
async function verifyChainDefaults(): Promise<void> {
  try {
    console.log('🔍 Verifying chain-specific default settings...\n');

    // Get all chain-specific settings
    const chainSettings = await db.select()
      .from(systemSettings)
      .where(or(
        like(systemSettings.id, '%_25'),
        like(systemSettings.id, '%_1'),
        like(systemSettings.id, '%_42161'),
        like(systemSettings.id, '%_11155111'),
        like(systemSettings.id, '%_sol1'),
        like(systemSettings.id, '%_388')
      ));

    // Group settings by chain
    const settingsByChain: Record<string, any[]> = {};

    chainSettings.forEach(setting => {
      const chainId = setting.id.split('_').pop() || 'unknown';
      if (!settingsByChain[chainId]) {
        settingsByChain[chainId] = [];
      }
      settingsByChain[chainId].push({
        type: setting.id.replace(`_${chainId}`, ''),
        id: setting.id,
        hasValue: !!setting.value
      });
    });

    // Display results
    const chainNames: Record<string, string> = {
      '25': 'Cronos',
      '1': 'Ethereum',
      '42161': 'Arbitrum',
      '11155111': 'Sepolia',
      'sol1': 'Solana',
      '388': 'Cronos zkEVM'
    };

    console.log('📊 Chain-specific settings summary:\n');

    for (const [chainId, chainName] of Object.entries(chainNames)) {
      console.log(`🔗 ${chainName} (${chainId}):`);

      const settings = settingsByChain[chainId] || [];
      const expectedTypes = ['component_defaults', 'profile_defaults', 'token_requirements', 'featured_profile'];

      expectedTypes.forEach(type => {
        const setting = settings.find(s => s.type === type);
        if (setting) {
          console.log(`  ✅ ${type}: ${setting.id}`);
        } else {
          console.log(`  ❌ ${type}: Missing`);
        }
      });

      console.log('');
    }

    // Show total count
    console.log(`📈 Total chain-specific settings: ${chainSettings.length}`);
    console.log(`🎯 Expected settings: ${Object.keys(chainNames).length * 4} (4 per chain)`);

    if (chainSettings.length === Object.keys(chainNames).length * 4) {
      console.log('🎉 All expected chain settings are present!');
    } else {
      console.log('⚠️  Some chain settings may be missing.');
    }

  } catch (error) {
    console.error('💥 Error verifying chain defaults:', error);
    throw error;
  }
}

// Run directly if this script is executed directly
if (require.main === module) {
  verifyChainDefaults()
    .then(() => {
      console.log('\n✨ Verification completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Verification failed:', error);
      process.exit(1);
    });
}

export default verifyChainDefaults;
