import { NextRequest } from 'next/server';
import { db } from '@/db/drizzle';
import { systemSettings, web3Profile } from '@/db/schema';
import { eq } from 'drizzle-orm';
import { getComponentDefaults } from '@/app/utils/systemSettings';

type Context = {
  params: Promise<{
    chainId: string;
  }>;
};

// GET chain-specific settings
export async function GET(
  _request: NextRequest,
  context: Context
): Promise<Response> {
  try {
    const { chainId } = await context.params;

    if (!chainId) {
      return Response.json(
        { error: 'Chain ID is required' },
        { status: 400 }
      );
    }

    // Get chain-specific settings
    const settingId = `chain_${chainId}_settings`;
    const settings = await db
      .select()
      .from(systemSettings)
      .where(eq(systemSettings.id, settingId));

    // Also get token requirements from the correct location
    const tokenRequirementsId = `token_requirements_${chainId}`;
    const tokenRequirementsSettings = await db
      .select()
      .from(systemSettings)
      .where(eq(systemSettings.id, tokenRequirementsId));

    if (settings.length === 0) {
      // Check for existing component defaults stored separately
      const existingComponentDefaults = await getComponentDefaults(chainId);

      // Get actual token requirements or use defaults
      let tokenRequirements = {
        mainRequirement: {
          chainId,
          chainName: '',
          tokenAddress: '',
          tokenName: 'Web3Tools',
          minimumHoldings: '',
          secondaryMinHoldings: ''
        },
        secondaryRequirements: []
      };

      if (tokenRequirementsSettings.length > 0) {
        tokenRequirements = tokenRequirementsSettings[0].value as any;
      }

      // Return default chain settings structure
      const defaultSettings = {
        chainId,
        tokenRequirements,
        profileDefaults: {
          default_role: 'user',
          default_status: 'new',
          default_expiry_days: '30',
          default_profile_name_format: 'address',
          default_profile_bio: ''
        },
        componentDefaults: {
          defaults: existingComponentDefaults
        },
        featuredProfile: 'web3tools'
      };

      return Response.json(defaultSettings, {
        headers: {
          'Cache-Control': 'no-store, no-cache, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });
    }

    // Check if existing settings have component defaults, if not, load them separately
    const existingSettings = settings[0].value as any;
    if (!existingSettings.componentDefaults || !existingSettings.componentDefaults.defaults || existingSettings.componentDefaults.defaults.length === 0) {
      const existingComponentDefaults = await getComponentDefaults(chainId);
      existingSettings.componentDefaults = {
        defaults: existingComponentDefaults
      };
    }

    // Override token requirements with the actual data from the correct location
    if (tokenRequirementsSettings.length > 0) {
      existingSettings.tokenRequirements = tokenRequirementsSettings[0].value as any;
    }

    return Response.json(existingSettings, {
      headers: {
        'Cache-Control': 'no-store, no-cache, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
  } catch (error) {
    return Response.json(
      { error: 'Failed to fetch chain settings' },
      { status: 500 }
    );
  }
}

// POST to update chain-specific settings
export async function POST(
  request: NextRequest,
  context: Context
): Promise<Response> {
  try {
    const { chainId } = await context.params;
    const data = await request.json();
    const { settingType, value, address } = data;

    if (!chainId || !settingType || !value) {
      return Response.json(
        { error: 'Chain ID, setting type, and value are required' },
        { status: 400 }
      );
    }

    // Check if user is admin
    if (address) {
      const user = await db
        .select()
        .from(web3Profile)
        .where(eq(web3Profile.address, address));

      if (user.length === 0 || user[0].role !== 'admin') {
        return Response.json(
          { error: 'Unauthorized: Only admins can update system settings' },
          { status: 403 }
        );
      }
    } else {
      return Response.json(
        { error: 'Unauthorized: Address is required' },
        { status: 403 }
      );
    }

    // Get existing chain settings or create new ones
    const settingId = `chain_${chainId}_settings`;
    const existingSettings = await db
      .select()
      .from(systemSettings)
      .where(eq(systemSettings.id, settingId));

    let currentSettings: any = {};
    if (existingSettings.length > 0) {
      currentSettings = existingSettings[0].value as any;
    }

    // Handle token requirements separately - they should be stored with their own ID format
    if (settingType === 'tokenRequirements') {
      const tokenRequirementsId = `token_requirements_${chainId}`;

      // Check if token requirements record exists
      const existingTokenRequirements = await db
        .select()
        .from(systemSettings)
        .where(eq(systemSettings.id, tokenRequirementsId));

      if (existingTokenRequirements.length === 0) {
        // Create new token requirements record
        await db.insert(systemSettings).values({
          id: tokenRequirementsId,
          value: value,
          updatedAt: new Date()
        });
      } else {
        // Update existing token requirements record
        await db.update(systemSettings)
          .set({
            value: value,
            updatedAt: new Date()
          })
          .where(eq(systemSettings.id, tokenRequirementsId));
      }
    } else {
      // Handle other setting types normally
      currentSettings[settingType] = value;

      if (existingSettings.length === 0) {
        // Create new chain settings
        await db.insert(systemSettings).values({
          id: settingId,
          value: currentSettings,
          updatedAt: new Date()
        });
      } else {
        // Update existing chain settings
        await db.update(systemSettings)
          .set({
            value: currentSettings,
            updatedAt: new Date()
          })
          .where(eq(systemSettings.id, settingId));
      }
    }

    return Response.json({ success: true });
  } catch (error) {
    console.error('Failed to update chain settings:', error);
    return Response.json(
      { error: 'Failed to update chain settings' },
      { status: 500 }
    );
  }
}
