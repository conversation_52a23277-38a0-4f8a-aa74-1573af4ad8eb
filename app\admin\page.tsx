'use client';

import { useState, useEffect } from 'react';
import { useAppKitAccount, useAppKitNetwork } from '@reown/appkit/react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  MoreHorizontal,
  Check,
  X,
  Clock,
  AlertTriangle,
  Trash2,
  Shield,
  User,
  ExternalLink,
  Plus,
  Loader2,
  RefreshCw,
  Play,
  Copy
} from 'lucide-react';
import { format } from 'date-fns';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import { CardFooter } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import TokenHolderTool from "@/components/TokenHolderTool";

// Define types
interface SystemSetting {
  id: string;
  value: any;
  createdAt: string;
  updatedAt: string;
}

interface ChainConfig {
  id: string;
  name: string;
}

interface MainTokenRequirement {
  chainId?: string;
  chainName?: string;
  tokenAddress: string;
  tokenName: string;
  minimumHoldings: string;
  secondaryMinHoldings: string;
}

interface PartnerToken {
  id: string;
  tokenAddress: string;
  tokenName: string;
  minimumHoldings: string;
}

interface SecondaryTokenRequirement {
  id: string;
  tokenAddress: string;
  tokenName: string;
  minimumHoldings: string;
}

interface TokenRequirements {
  mainRequirement: MainTokenRequirement;
  secondaryRequirements: SecondaryTokenRequirement[];
}



interface ChainSettings {
  chainId: string;
  chainName: string;
  // Token Requirements
  mainToken: MainTokenRequirement;
  secondaryTokens: SecondaryTokenRequirement[];
  // Profile Defaults for this chain
  profileDefaults: {
    default_role: string;
    default_status: string;
    default_bio: string;
    default_location: string;
    default_website: string;
    default_twitter: string;
    default_discord: string;
    default_telegram: string;
    default_github: string;
    default_linkedin: string;
    default_youtube: string;
    default_instagram: string;
    default_tiktok: string;
    default_twitch: string;
    default_reddit: string;
    default_medium: string;
    default_substack: string;
    default_email: string;
  };
  // Component Defaults for this chain
  componentDefaults: {
    hero_enabled: boolean;
    social_links_enabled: boolean;
    about_enabled: boolean;
    skills_enabled: boolean;
    experience_enabled: boolean;
    projects_enabled: boolean;
    education_enabled: boolean;
    achievements_enabled: boolean;
    testimonials_enabled: boolean;
    contact_enabled: boolean;
  };
  // Featured Profile for this chain
  featuredProfile: string;
}

interface SystemSettings {
  chains: ChainSettings[];
}

interface ProfileDefaults {
  default_role: string;
  default_status: string;
  default_expiry_days: string;
  default_profile_name_format: string;
  default_profile_bio: string;
}

interface ComponentDefault {
  componentType: string;
  order: string;
  hidden: string;
  details: {
    backgroundColor?: string;
    fontColor?: string | null;
    scale?: string;
    positionX?: number;
    positionY?: number;
    naturalWidth?: number | null;
    naturalHeight?: number | null;
    shape?: string;
    profileName?: string;
    profileBio?: string;
    defaultImagePath?: string;
    defaultBannerImagePath?: string;
    defaultProfileImagePath?: string;
    heroContent?: Array<{
      title: string;
      description: string;
      contentType: string;
      colorGradient?: string;
    }>;
    socialLinks?: {
      twitter?: string;
      discord?: string;
      telegram?: string;
      website?: string;
      facebook?: string;
      youtube?: string;
      email?: string;
      linkedin?: string;
      cro?: string;
    };
  };
}

interface ComponentDefaults {
  defaults: ComponentDefault[];
}

interface User {
  address: string;
  chain: string; // Add chain field
  name?: string; // Optional for backward compatibility
  profileName?: string; // New field
  role: 'admin' | 'user';
  status: 'new' | 'in-progress' | 'pending' | 'approved' | 'deleted';
  expiryDate: string | null;
  transactionHash: string | null;
  referralCode?: string | null; // Referral code for the user
  referredBy?: string | null; // Referral code of who referred this user
  createdAt: string;
  updatedAt: string;
}

export default function AdminPage() {
  const [users, setUsers] = useState<User[]>([]);
  const [allUsers, setAllUsers] = useState<User[]>([]); // Store all users for filtering
  const [settings, setSettings] = useState<SystemSetting[]>([]);
  const [featuredProfile, setFeaturedProfile] = useState<string>('web3tools');
  const [systemSettings, setSystemSettings] = useState<SystemSettings>({
    chains: []
  });
  const [selectedChainId, setSelectedChainId] = useState<string>('');
  const [availableChains, setAvailableChains] = useState<ChainConfig[]>([
    { id: '25', name: 'Cronos' },
    { id: '1', name: 'Ethereum' },
    { id: '137', name: 'Polygon' },
    { id: '56', name: 'BNB Chain' },
    { id: '42161', name: 'Arbitrum' },
    { id: 'sol1', name: 'Solana' },
    { id: '5eykt4UsFv8P8NJdTREpY1vzqKqZKvdp', name: 'Solana Mainnet' }
  ]);
  const [loading, setLoading] = useState(true);
  const [settingsLoading, setSettingsLoading] = useState(true);
  const [isAdmin, setIsAdmin] = useState(false);
  const [adminCheckComplete, setAdminCheckComplete] = useState(false);
  const [activeTab, setActiveTab] = useState('users');

  // Add missing state declarations
  const [tokenRequirements, setTokenRequirements] = useState<TokenRequirements>({
    mainRequirement: {
      chainId: '25',
      chainName: 'Cronos',
      tokenAddress: '',
      tokenName: 'Web3Tools',
      minimumHoldings: '',
      secondaryMinHoldings: ''
    },
    secondaryRequirements: []
  });

  const [profileDefaults, setProfileDefaults] = useState<ProfileDefaults>({
    default_role: 'user',
    default_status: 'new',
    default_expiry_days: '30',
    default_profile_name_format: 'address',
    default_profile_bio: ''
  });

  const [componentDefaults, setComponentDefaults] = useState<ComponentDefaults>({
    defaults: []
  });

  // Balance check state
  const [isBalanceChecking, setIsBalanceChecking] = useState(false);
  const [balanceCheckResult, setBalanceCheckResult] = useState<{
    jobId?: string;
    totalChecked?: number;
    totalStatusChanged?: number;
    totalErrors?: number;
    message?: string;
    statusChangedProfiles?: Array<{
      address: string;
      name: string;
      chain: string;
      oldStatus: string;
      newStatus: string;
      balanceCheckResult: any;
      tokenRequirements: any;
    }>;
  } | null>(null);

  // Bulk approve state
  const [isBulkApproving, setIsBulkApproving] = useState(false);
  const [bulkApproveResult, setBulkApproveResult] = useState<{
    jobId?: string;
    totalChecked?: number;
    totalStatusChanged?: number;
    totalErrors?: number;
    message?: string;
    statusChangedProfiles?: Array<{
      address: string;
      name: string;
      chain: string;
      oldStatus: string;
      newStatus: string;
      balanceCheckResult: any;
      tokenRequirements: any;
    }>;
  } | null>(null);

  // SQL Query and Profile Transfer states
  const [sqlQuery, setSqlQuery] = useState<string>('');
  const [sqlResult, setSqlResult] = useState<any>(null);
  const [isExecutingQuery, setIsExecutingQuery] = useState(false);
  const [fromAddress, setFromAddress] = useState<string>('');
  const [toAddress, setToAddress] = useState<string>('');

  // Execute SQL query
  const executeSqlQuery = async () => {
    if (!sqlQuery.trim()) {
      toast.error('Please enter a SQL query');
      return;
    }

    setIsExecutingQuery(true);
    try {
      const response = await fetch('/api/admin/database/query', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query: sqlQuery }),
      });

      if (!response.ok) {
        throw new Error('Failed to execute query');
      }

      const result = await response.json();
      setSqlResult(result);
      toast.success('Query executed successfully');
    } catch (error) {
      console.error('Error executing query:', error);
      toast.error('Failed to execute query');
      setSqlResult({ error: error instanceof Error ? error.message : 'Unknown error' });
    } finally {
      setIsExecutingQuery(false);
    }
  };

  // Transfer profile from one address to another
  const transferProfile = async () => {
    if (!fromAddress || !toAddress) {
      toast.error('Please enter both from and to addresses');
      return;
    }

    if (fromAddress === toAddress) {
      toast.error('From and to addresses cannot be the same');
      return;
    }

    if (!confirm(`Are you sure you want to transfer all data from ${fromAddress} to ${toAddress}? This will update all database records. If ${toAddress} already has a profile, it will be completely deleted and replaced.`)) {
      return;
    }

    try {
      const response = await fetch('/api/admin/database/transfer-profile', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fromAddress,
          toAddress
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to transfer profile');
      }

      const result = await response.json();
      let message = `Profile transferred successfully! Updated ${result.totalUpdated} records.`;
      if (result.deletedRecords) {
        message += ` Deleted ${result.deletedRecords} existing records from destination.`;
      }
      toast.success(message);
      setFromAddress('');
      setToAddress('');
    } catch (error) {
      console.error('Error transferring profile:', error);
      toast.error('Failed to transfer profile');
    }
  };

  const { address, isConnected } = useAppKitAccount();
  const { chainId } = useAppKitNetwork();
  const router = useRouter();

  // Fetch chain-specific settings
  const fetchChainSettings = async (chainId: string) => {
    if (!chainId) return;

    try {
      setSettingsLoading(true);
      const response = await fetch(`/api/admin/settings/chain/${chainId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch chain settings');
      }
      const chainSettings = await response.json();

      // Update state with chain-specific settings
      if (chainSettings.tokenRequirements) {
        setTokenRequirements(chainSettings.tokenRequirements);
      }
      if (chainSettings.profileDefaults) {
        setProfileDefaults(chainSettings.profileDefaults);
      }
      if (chainSettings.componentDefaults) {
        setComponentDefaults(chainSettings.componentDefaults);
      }
      if (chainSettings.featuredProfile) {
        setFeaturedProfile(chainSettings.featuredProfile);
      } else {
        setFeaturedProfile('web3tools'); // Default value
      }
    } catch (error) {
      console.error('Error fetching chain settings:', error);
      toast.error('Failed to fetch chain settings');
    } finally {
      setSettingsLoading(false);
    }
  };



  // Filter users by selected chain
  const filterUsersByChain = (allUsers: User[], chainId: string) => {
    if (!chainId || chainId === 'all') return allUsers;
    return allUsers.filter(user => user.chain === chainId);
  };

  // Update users when chain selection changes
  useEffect(() => {
    const filteredUsers = filterUsersByChain(allUsers, selectedChainId);
    setUsers(filteredUsers);
  }, [allUsers, selectedChainId]);

  // Set default chain to current connected chain
  useEffect(() => {
    if (chainId) {
      const connectedChainId = chainId.toString();

      // Check if the connected chain is in our available chains list
      const isChainSupported = availableChains.some(chain => chain.id === connectedChainId);

      if (isChainSupported) {
        // Always update to the connected chain when wallet connects or chain changes
        if (connectedChainId !== selectedChainId) {
          setSelectedChainId(connectedChainId);
          console.log('Admin - Setting selected chain to connected chainId:', connectedChainId);
        }
      } else {
        // Add the unknown chain to the list
        const unknownChain = { id: connectedChainId, name: `Chain ${connectedChainId}` };
        setAvailableChains(prev => [...prev, unknownChain]);
        setSelectedChainId(connectedChainId);
        console.log('Admin - Added unknown chain and selected:', connectedChainId);
      }
    }
  }, [chainId, selectedChainId, availableChains]);

  // Fetch chain-specific settings when selected chain changes
  useEffect(() => {
    if (selectedChainId && selectedChainId !== 'all') {
      fetchChainSettings(selectedChainId);
    }
  }, [selectedChainId]);

  // Fetch users and check if current user is admin
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setLoading(true);
        console.log('Admin check - Starting fetchUsers, isConnected:', isConnected, 'address:', address);

        const response = await fetch('/api/admin/users');
        if (!response.ok) {
          throw new Error('Failed to fetch users');
        }
        const data = await response.json();
        console.log('Admin check - Fetched users data:', data);
        setAllUsers(data); // Store all users

        // Filter users by selected chain
        const filteredUsers = filterUsersByChain(data, selectedChainId);
        setUsers(filteredUsers);

        // Check if current user is admin
        if (address) {
          console.log('Admin check - Connected address:', address);
          console.log('Admin check - Users data length:', data.length);
          console.log('Admin check - Looking for user with address:', address.toLowerCase());

          const currentUser = data.find((user: User) => user.address.toLowerCase() === address.toLowerCase());
          console.log('Admin check - Found user:', currentUser);

          if (currentUser && currentUser.role === 'admin') {
            console.log('Admin check - User is admin, setting isAdmin to true');
            setIsAdmin(true);
          } else {
            console.log('Admin check - User is not admin or not found');
            console.log('Admin check - All user addresses:', data.map((u: User) => u.address));
            setIsAdmin(false);
          }
        }
      } catch (error) {
        console.error('Error fetching users:', error);
        toast.error('Failed to fetch users');
        setIsAdmin(false);
      } finally {
        setLoading(false);
      }
    };

    if (isConnected && address && chainId) {
      console.log('Admin check - Wallet connected, fetching users');
      fetchUsers();
    } else {
      console.log('Admin check - Wallet not connected, no address, or no chainId');
      setLoading(false);
      setIsAdmin(false);
    }
  }, [isConnected, address, chainId, router]);

  // Update user role
  const updateUserRole = async (userAddress: string, role: 'admin' | 'user') => {
    try {
      const response = await fetch(`/api/admin/users/${userAddress}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ role }),
      });

      if (!response.ok) {
        throw new Error('Failed to update user role');
      }

      const updatedUser = await response.json();
      setUsers(users.map(user => user.address === userAddress ? updatedUser : user));
      toast.success(`User role updated to ${role}`);
    } catch (error) {
      console.error('Error updating user role:', error);
      toast.error('Failed to update user role');
    }
  };

  // Update user status
  const updateUserStatus = async (userAddress: string, status: 'new' | 'in-progress' | 'pending' | 'approved' | 'deleted') => {
    try {
      // If status is approved, also set expiryDate to null
      const updateData: { status: string; expiryDate?: null } = { status };
      if (status === 'approved') {
        updateData.expiryDate = null;
      }

      const response = await fetch(`/api/admin/users/${userAddress}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        throw new Error('Failed to update user status');
      }

      const updatedUser = await response.json();
      setUsers(users.map(user => user.address === userAddress ? updatedUser : user));
      toast.success(`User status updated to ${status}`);
    } catch (error) {
      console.error('Error updating user status:', error);
      toast.error('Failed to update user status');
    }
  };

  // Update transaction hash
  const updateTransactionHash = async (userAddress: string, transactionHash: string) => {
    try {
      const response = await fetch(`/api/admin/users/${userAddress}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ transactionHash }),
      });

      if (!response.ok) {
        throw new Error('Failed to update transaction hash');
      }

      const updatedUser = await response.json();
      setUsers(users.map(user => user.address === userAddress ? updatedUser : user));
      toast.success('Transaction hash updated');
    } catch (error) {
      console.error('Error updating transaction hash:', error);
      toast.error('Failed to update transaction hash');
    }
  };

  // Update token requirements (chain-specific)
  const updateTokenRequirements = async () => {
    if (!selectedChainId || selectedChainId === 'all') {
      toast.error('Please select a specific chain first');
      return;
    }

    console.log('=== ADMIN SAVE DEBUG ===');
    console.log('Selected Chain ID:', selectedChainId);
    console.log('Token Requirements being saved:', JSON.stringify(tokenRequirements, null, 2));

    try {
      const response = await fetch(`/api/admin/settings/chain/${selectedChainId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          settingType: 'tokenRequirements',
          value: tokenRequirements,
          address: address
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update token requirements');
      }

      toast.success(`Token requirements updated for ${availableChains.find(c => c.id === selectedChainId)?.name || selectedChainId}`);
    } catch (error) {
      console.error('Error updating token requirements:', error);
      toast.error('Failed to update token requirements');
    }
  };

  // Update component defaults (chain-specific)
  const updateComponentDefaults = async () => {
    if (!selectedChainId || selectedChainId === 'all') {
      toast.error('Please select a specific chain first');
      return;
    }

    try {
      const response = await fetch(`/api/admin/settings/chain/${selectedChainId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          settingType: 'componentDefaults',
          value: componentDefaults,
          address: address
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update component defaults');
      }

      toast.success(`Component defaults updated for ${availableChains.find(c => c.id === selectedChainId)?.name || selectedChainId}`);
    } catch (error) {
      console.error('Error updating component defaults:', error);
      toast.error('Failed to update component defaults');
    }
  };

  // Update profile defaults (chain-specific)
  const updateProfileDefaults = async () => {
    if (!selectedChainId || selectedChainId === 'all') {
      toast.error('Please select a specific chain first');
      return;
    }

    try {
      const response = await fetch(`/api/admin/settings/chain/${selectedChainId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          settingType: 'profileDefaults',
          value: profileDefaults,
          address: address
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update profile defaults');
      }

      toast.success(`Profile defaults updated for ${availableChains.find(c => c.id === selectedChainId)?.name || selectedChainId}`);
    } catch (error) {
      console.error('Error updating profile defaults:', error);
      toast.error('Failed to update profile defaults');
    }
  };

  // Update featured profile (chain-specific)
  const updateFeaturedProfile = async () => {
    if (!selectedChainId || selectedChainId === 'all') {
      toast.error('Please select a specific chain first');
      return;
    }

    try {
      const response = await fetch(`/api/admin/settings/chain/${selectedChainId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          settingType: 'featuredProfile',
          value: featuredProfile,
          address: address
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update featured profile');
      }

      toast.success(`Featured profile updated for ${availableChains.find(c => c.id === selectedChainId)?.name || selectedChainId}`);
    } catch (error) {
      console.error('Error updating featured profile:', error);
      toast.error('Failed to update featured profile');
    }
  };

  // Update expiry date
  const updateExpiryDate = async (userAddress: string, expiryDate: string | null) => {
    try {
      const response = await fetch(`/api/admin/users/${userAddress}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ expiryDate }),
      });

      if (!response.ok) {
        throw new Error('Failed to update expiry date');
      }

      const updatedUser = await response.json();
      setUsers(users.map(user => user.address === userAddress ? updatedUser : user));
      toast.success('Expiry date updated');
    } catch (error) {
      console.error('Error updating expiry date:', error);
      toast.error('Failed to update expiry date');
    }
  };

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'new':
        return <Clock className="h-4 w-4 text-blue-500" />;
      case 'in-progress':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'pending':
        return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      case 'approved':
        return <Check className="h-4 w-4 text-green-500" />;
      case 'deleted':
        return <Trash2 className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  // Get role icon
  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin':
        return <Shield className="h-4 w-4 text-purple-500" />;
      case 'user':
        return <User className="h-4 w-4 text-blue-500" />;
      default:
        return <User className="h-4 w-4" />;
    }
  };

  // Format date
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    try {
      return format(new Date(dateString), 'MMM dd, yyyy HH:mm');
    } catch (error) {
      return 'Invalid date';
    }
  };

  // Update main token requirement
  const updateMainRequirement = (field: keyof MainTokenRequirement, value: string) => {
    console.log('=== updateMainRequirement DEBUG ===');
    console.log('Field:', field);
    console.log('Value:', value);
    console.log('Previous state:', JSON.stringify(tokenRequirements, null, 2));

    setTokenRequirements(prev => {
      const updated = {
        ...prev,
        mainRequirement: {
          ...prev.mainRequirement,
          chainId: prev.mainRequirement?.chainId || '25',
          chainName: prev.mainRequirement?.chainName || 'Cronos',
          tokenAddress: prev.mainRequirement?.tokenAddress || '',
          tokenName: prev.mainRequirement?.tokenName || 'Web3Tools',
          minimumHoldings: prev.mainRequirement?.minimumHoldings || '',
          secondaryMinHoldings: prev.mainRequirement?.secondaryMinHoldings || '',

          [field]: value
        }
      };

      console.log('Updated state:', JSON.stringify(updated, null, 2));
      return updated;
    });
  };

  // Add new secondary token requirement
  const addSecondaryTokenRequirement = () => {
    const newSecondaryToken: SecondaryTokenRequirement = {
      id: Date.now().toString(),
      tokenAddress: '',
      tokenName: '',
      minimumHoldings: ''
    };

    setTokenRequirements(prev => ({
      ...prev,
      secondaryRequirements: [...prev.secondaryRequirements, newSecondaryToken]
    }));
  };

  // Remove secondary token requirement
  const removeSecondaryTokenRequirement = (tokenId: string) => {
    setTokenRequirements(prev => ({
      ...prev,
      secondaryRequirements: prev.secondaryRequirements.filter(req => req.id !== tokenId)
    }));
  };

  // Update secondary token requirement
  const updateSecondaryTokenRequirement = (tokenId: string, field: keyof SecondaryTokenRequirement, value: string) => {
    setTokenRequirements(prev => ({
      ...prev,
      secondaryRequirements: prev.secondaryRequirements.map(req =>
        req.id === tokenId ? { ...req, [field]: value } : req
      )
    }));
  };



  // Add component default
  const addComponentDefault = () => {
    setComponentDefaults(prev => ({
      defaults: [
        ...(prev.defaults || []),
        {
          componentType: '',
          order: '',
          hidden: 'N',
          details: {
            backgroundColor: 'transparent',
            fontColor: null
          }
        }
      ]
    }));
  };

  // Remove component default
  const removeComponentDefault = (index: number) => {
    setComponentDefaults(prev => ({
      defaults: (prev.defaults || []).filter((_, i) => i !== index)
    }));
  };

  // Update component default
  const updateComponentDefault = (index: number, field: keyof ComponentDefault, value: string) => {
    setComponentDefaults(prev => {
      if (!prev.defaults || prev.defaults.length <= index) {
        return prev; // Return unchanged if defaults is undefined or index is out of bounds
      }
      const newDefaults = [...prev.defaults];
      newDefaults[index] = {
        ...newDefaults[index],
        [field]: value
      };
      return { defaults: newDefaults };
    });
  };

  // Update component details
  const updateComponentDetails = (index: number, detailField: string, value: any) => {
    setComponentDefaults(prev => {
      if (!prev.defaults || prev.defaults.length <= index) {
        return prev; // Return unchanged if defaults is undefined or index is out of bounds
      }
      const newDefaults = [...prev.defaults];
      newDefaults[index] = {
        ...newDefaults[index],
        details: {
          ...(newDefaults[index].details || {}),
          [detailField]: value
        }
      };
      return { defaults: newDefaults };
    });
  };

  // Get Cronoscan URL
  const getCronoscanUrl = (hash: string) => {
    return `https://cronoscan.com/tx/${hash}`;
  };

  // Check all approved profiles' balances
  const checkAllBalances = async () => {
    if (isBalanceChecking) return;

    try {
      setIsBalanceChecking(true);
      setBalanceCheckResult(null);

      console.log('Starting bulk balance check for all approved profiles...');
      toast.info('Starting balance check for all approved profiles...');

      const response = await fetch('/api/admin/balance-check', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to start balance check');
      }

      const result = await response.json();
      console.log('Balance check completed:', result);

      setBalanceCheckResult({
        jobId: result.jobId,
        totalChecked: result.summary?.totalChecked,
        totalStatusChanged: result.summary?.totalStatusChanged,
        totalErrors: result.summary?.totalErrors,
        message: result.message,
        statusChangedProfiles: result.summary?.statusChangedProfiles || []
      });

      if (result.success) {
        toast.success(result.message);

        // Refresh users list to show updated statuses
        const usersResponse = await fetch('/api/admin/users');
        if (usersResponse.ok) {
          const updatedUsers = await usersResponse.json();
          setAllUsers(updatedUsers);
        }
      } else {
        toast.error(result.error || 'Balance check failed');
      }

    } catch (error) {
      console.error('Error during balance check:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to check balances');
      setBalanceCheckResult({
        message: 'Balance check failed: ' + (error instanceof Error ? error.message : 'Unknown error')
      });
    } finally {
      setIsBalanceChecking(false);
    }
  };

  // Bulk approve pending profiles that meet token requirements
  const bulkApprovePending = async () => {
    if (isBulkApproving) return;

    try {
      setIsBulkApproving(true);
      setBulkApproveResult(null);

      console.log('Starting bulk approve for all pending profiles...');
      toast.info('Starting bulk approve for all pending profiles...');

      const response = await fetch('/api/admin/bulk-approve', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to start bulk approve');
      }

      const result = await response.json();
      console.log('Bulk approve completed:', result);

      setBulkApproveResult({
        jobId: result.jobId,
        totalChecked: result.summary?.totalChecked,
        totalStatusChanged: result.summary?.totalStatusChanged,
        totalErrors: result.summary?.totalErrors,
        message: result.message,
        statusChangedProfiles: result.summary?.statusChangedProfiles || []
      });

      if (result.success) {
        toast.success(result.message);

        // Refresh users list to show updated statuses
        const usersResponse = await fetch('/api/admin/users');
        if (usersResponse.ok) {
          const updatedUsers = await usersResponse.json();
          setAllUsers(updatedUsers);
        }
      } else {
        toast.error(result.error || 'Bulk approve failed');
      }
    } catch (error) {
      console.error('Error during bulk approve:', error);
      toast.error('Failed to bulk approve: ' + (error instanceof Error ? error.message : 'Unknown error'));
      setBulkApproveResult({
        message: 'Bulk approve failed: ' + (error instanceof Error ? error.message : 'Unknown error')
      });
    } finally {
      setIsBulkApproving(false);
    }
  };













  // Show loading state while checking connection and admin status
  if (loading) {
    return (
      <div className="container mx-auto p-4">
        <h1 className="text-2xl font-bold mb-4">Admin Dashboard</h1>
        <div className="flex items-center gap-2">
          <Loader2 className="h-4 w-4 animate-spin" />
          <p>Loading admin dashboard...</p>
        </div>
        <div className="mt-4 text-sm text-gray-600">
          <p>Connected: {String(isConnected)}</p>
          <p>Address: {address || 'None'}</p>
          <p>Network: {chainId ? `Chain ${chainId}` : 'Not connected'}</p>
          <p>Admin Status: {isAdmin ? 'Admin' : 'Checking...'}</p>
        </div>
      </div>
    );
  }

  // Show connection required message
  if (!isConnected || !address || !chainId) {
    return (
      <div className="container mx-auto p-4">
        <h1 className="text-2xl font-bold mb-4">Admin Dashboard</h1>
        <p>Please connect your wallet and ensure you're on a supported network to access the admin page.</p>
        <div className="mt-4 text-sm text-gray-600">
          <p>Connected: {String(isConnected)}</p>
          <p>Address: {address ? 'Yes' : 'No'}</p>
          <p>Network: {chainId ? `Chain ${chainId}` : 'Not connected'}</p>
        </div>
      </div>
    );
  }

  // Show access denied message
  if (!isAdmin) {
    return (
      <div className="container mx-auto p-4">
        <h1 className="text-2xl font-bold mb-4">Admin Dashboard</h1>
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <p className="text-red-800 dark:text-red-200">You do not have permission to access this page.</p>
          <p className="text-sm text-red-600 dark:text-red-400 mt-2">
            Connected as: {address}
          </p>
          <p className="text-sm text-red-600 dark:text-red-400">
            Only admin users can access this dashboard.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Admin Dashboard</h1>

      {/* Chain-Specific Settings Header */}
      <div className="mb-6">
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <div className="flex items-center gap-2">
            <div className="h-2 w-2 bg-blue-500 rounded-full"></div>
            <p className="text-blue-800 dark:text-blue-200 font-medium">
              Chain-Specific Settings
            </p>
          </div>
          <p className="text-sm text-blue-600 dark:text-blue-400 mt-1">
            You are configuring settings for <strong>{availableChains.find(c => c.id === selectedChainId)?.name || selectedChainId}</strong>. These settings will only apply to users and profiles on this blockchain.
          </p>
        </div>
      </div>

      <Tabs defaultValue="users" className="w-full mb-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="users">User Management</TabsTrigger>
          <TabsTrigger value="settings">System Settings</TabsTrigger>
          <TabsTrigger value="token-holders">Token Holders</TabsTrigger>
          <TabsTrigger value="database">Database Tools</TabsTrigger>
        </TabsList>

        <TabsContent value="users" className="mt-4">
          {/* Balance Check Section */}
          <div className="mb-6 p-4 border rounded-lg bg-orange-50 dark:bg-orange-900/20 border-orange-200 dark:border-orange-800">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-lg font-semibold text-orange-800 dark:text-orange-200">
                  Bulk Balance Check
                </h3>
                <p className="text-sm text-orange-600 dark:text-orange-400">
                  Check all approved profiles to verify they still meet token requirements.
                  Profiles that no longer meet requirements will be changed from 'approved' to 'new' status.
                </p>
              </div>
              <Button
                onClick={checkAllBalances}
                disabled={isBalanceChecking}
                variant="outline"
                className="border-orange-300 text-orange-700 hover:bg-orange-100 dark:border-orange-700 dark:text-orange-300 dark:hover:bg-orange-900/30"
              >
                {isBalanceChecking ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Checking Balances...
                  </>
                ) : (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Check All Balances
                  </>
                )}
              </Button>
            </div>

            {/* Balance Check Results */}
            {balanceCheckResult && (
              <div className="mt-4 p-3 bg-white dark:bg-gray-800 border rounded-md">
                <h4 className="font-medium mb-2">Balance Check Results</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  {balanceCheckResult.totalChecked !== undefined && (
                    <div>
                      <span className="font-medium">Total Checked:</span>
                      <span className="ml-2">{balanceCheckResult.totalChecked}</span>
                    </div>
                  )}
                  {balanceCheckResult.totalStatusChanged !== undefined && (
                    <div>
                      <span className="font-medium">Status Changed:</span>
                      <span className="ml-2 text-red-600 dark:text-red-400">
                        {balanceCheckResult.totalStatusChanged}
                      </span>
                    </div>
                  )}
                  {balanceCheckResult.totalErrors !== undefined && (
                    <div>
                      <span className="font-medium">Errors:</span>
                      <span className="ml-2 text-yellow-600 dark:text-yellow-400">
                        {balanceCheckResult.totalErrors}
                      </span>
                    </div>
                  )}
                </div>
                {balanceCheckResult.message && (
                  <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                    {balanceCheckResult.message}
                  </p>
                )}
                {balanceCheckResult.jobId && (
                  <p className="mt-1 text-xs text-gray-500 dark:text-gray-500">
                    Job ID: {balanceCheckResult.jobId}
                  </p>
                )}

                {/* Status Changed Profiles Table */}
                {balanceCheckResult.statusChangedProfiles && balanceCheckResult.statusChangedProfiles.length > 0 && (
                  <div className="mt-4">
                    <h5 className="font-medium mb-3 text-red-700 dark:text-red-300">
                      Profiles Changed from Approved to New ({balanceCheckResult.statusChangedProfiles.length})
                    </h5>
                    <div className="overflow-x-auto">
                      <table className="min-w-full text-sm border border-gray-200 dark:border-gray-700">
                        <thead className="bg-gray-50 dark:bg-gray-700">
                          <tr>
                            <th className="px-3 py-2 text-left font-medium text-gray-900 dark:text-gray-100 border-b">
                              Profile
                            </th>
                            <th className="px-3 py-2 text-left font-medium text-gray-900 dark:text-gray-100 border-b">
                              Chain
                            </th>
                            <th className="px-3 py-2 text-left font-medium text-gray-900 dark:text-gray-100 border-b">
                              Validation Result
                            </th>
                            <th className="px-3 py-2 text-left font-medium text-gray-900 dark:text-gray-100 border-b">
                              Token Holdings
                            </th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                          {balanceCheckResult.statusChangedProfiles.map((profile, index) => (
                            <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                              <td className="px-3 py-2 border-b">
                                <div>
                                  <div className="font-medium text-gray-900 dark:text-gray-100">
                                    {profile.name}
                                  </div>
                                  <div className="text-xs text-gray-500 dark:text-gray-400 font-mono">
                                    {profile.address.slice(0, 8)}...{profile.address.slice(-6)}
                                  </div>
                                </div>
                              </td>
                              <td className="px-3 py-2 border-b">
                                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                  {profile.chain}
                                </span>
                              </td>
                              <td className="px-3 py-2 border-b">
                                <div className="text-xs">
                                  <div className="text-red-600 dark:text-red-400 font-medium">
                                    {profile.balanceCheckResult?.validationPath || 'Failed validation'}
                                  </div>
                                  {profile.balanceCheckResult?.message && (
                                    <div className="text-gray-600 dark:text-gray-400 mt-1">
                                      {profile.balanceCheckResult.message}
                                    </div>
                                  )}
                                </div>
                              </td>
                              <td className="px-3 py-2 border-b">
                                <div className="text-xs space-y-1">
                                  {/* Main Token Balance */}
                                  {profile.balanceCheckResult?.mainTokenResult && (
                                    <div>
                                      <span className="font-medium">Main:</span>
                                      <span className="ml-1 text-red-600 dark:text-red-400">
                                        {profile.balanceCheckResult.mainTokenResult.currentBalance}
                                      </span>
                                      <span className="text-gray-500 dark:text-gray-400 ml-1">
                                        (req: {profile.balanceCheckResult.mainTokenResult.minimumRequired})
                                      </span>
                                    </div>
                                  )}
                                  {/* Secondary Token Results */}
                                  {profile.balanceCheckResult?.secondaryTokenResults?.map((result: any, idx: number) => (
                                    <div key={idx}>
                                      <span className="font-medium">Secondary {idx + 1}:</span>
                                      <span className="ml-1 text-red-600 dark:text-red-400">
                                        {result.currentBalance}
                                      </span>
                                      <span className="text-gray-500 dark:text-gray-400 ml-1">
                                        (req: {result.minimumRequired})
                                      </span>
                                    </div>
                                  ))}
                                  {/* Secondary Min Holdings */}
                                  {profile.balanceCheckResult?.secondaryMinHoldingsResult && (
                                    <div>
                                      <span className="font-medium">Min Holdings:</span>
                                      <span className="ml-1 text-red-600 dark:text-red-400">
                                        {profile.balanceCheckResult.secondaryMinHoldingsResult.currentBalance}
                                      </span>
                                      <span className="text-gray-500 dark:text-gray-400 ml-1">
                                        (req: {profile.balanceCheckResult.secondaryMinHoldingsResult.minimumRequired})
                                      </span>
                                    </div>
                                  )}
                                </div>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Bulk Approve Section */}
          <div className="mb-6 p-4 border rounded-lg bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-lg font-semibold text-green-800 dark:text-green-200">
                  Bulk Approve Pending
                </h3>
                <p className="text-sm text-green-600 dark:text-green-400">
                  Check all pending profiles and approve those that meet token requirements.
                  Profiles that meet requirements will be changed from 'pending' to 'approved' status.
                </p>
              </div>
              <Button
                onClick={bulkApprovePending}
                disabled={isBulkApproving}
                variant="outline"
                className="border-green-300 text-green-700 hover:bg-green-100 dark:border-green-700 dark:text-green-300 dark:hover:bg-green-900/30"
              >
                {isBulkApproving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Approving...
                  </>
                ) : (
                  <>
                    <Check className="mr-2 h-4 w-4" />
                    Bulk Approve Pending
                  </>
                )}
              </Button>
            </div>

            {/* Bulk Approve Results */}
            {bulkApproveResult && (
              <div className="mt-4 p-3 bg-white dark:bg-gray-800 border rounded-md">
                <h4 className="font-medium mb-2">Bulk Approve Results</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  {bulkApproveResult.totalChecked !== undefined && (
                    <div>
                      <span className="font-medium">Total Checked:</span>
                      <span className="ml-2">{bulkApproveResult.totalChecked}</span>
                    </div>
                  )}
                  {bulkApproveResult.totalStatusChanged !== undefined && (
                    <div>
                      <span className="font-medium">Approved:</span>
                      <span className="ml-2 text-green-600 dark:text-green-400">
                        {bulkApproveResult.totalStatusChanged}
                      </span>
                    </div>
                  )}
                  {bulkApproveResult.totalErrors !== undefined && (
                    <div>
                      <span className="font-medium">Errors:</span>
                      <span className="ml-2 text-yellow-600 dark:text-yellow-400">
                        {bulkApproveResult.totalErrors}
                      </span>
                    </div>
                  )}
                </div>
                {bulkApproveResult.message && (
                  <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                    {bulkApproveResult.message}
                  </p>
                )}
                {bulkApproveResult.jobId && (
                  <p className="mt-1 text-xs text-gray-500 dark:text-gray-500">
                    Job ID: {bulkApproveResult.jobId}
                  </p>
                )}

                {/* Status Changed Profiles Table */}
                {bulkApproveResult.statusChangedProfiles && bulkApproveResult.statusChangedProfiles.length > 0 && (
                  <div className="mt-4">
                    <h5 className="font-medium mb-3 text-green-700 dark:text-green-300">
                      Profiles Approved ({bulkApproveResult.statusChangedProfiles.length})
                    </h5>
                    <div className="overflow-x-auto">
                      <table className="min-w-full text-sm border border-gray-200 dark:border-gray-700">
                        <thead className="bg-gray-50 dark:bg-gray-700">
                          <tr>
                            <th className="px-3 py-2 text-left font-medium text-gray-900 dark:text-gray-100 border-b">
                              Profile
                            </th>
                            <th className="px-3 py-2 text-left font-medium text-gray-900 dark:text-gray-100 border-b">
                              Chain
                            </th>
                            <th className="px-3 py-2 text-left font-medium text-gray-900 dark:text-gray-100 border-b">
                              Validation Result
                            </th>
                            <th className="px-3 py-2 text-left font-medium text-gray-900 dark:text-gray-100 border-b">
                              Token Holdings
                            </th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                          {bulkApproveResult.statusChangedProfiles.map((profile, index) => (
                            <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                              <td className="px-3 py-2 border-b">
                                <div>
                                  <div className="font-medium text-gray-900 dark:text-gray-100">
                                    {profile.name}
                                  </div>
                                  <div className="text-xs text-gray-500 dark:text-gray-400 font-mono">
                                    {profile.address.slice(0, 8)}...{profile.address.slice(-6)}
                                  </div>
                                </div>
                              </td>
                              <td className="px-3 py-2 border-b">
                                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                  {profile.chain}
                                </span>
                              </td>
                              <td className="px-3 py-2 border-b">
                                <div className="text-xs">
                                  <div className="text-green-600 dark:text-green-400 font-medium">
                                    {profile.balanceCheckResult?.validationPath || 'Passed validation'}
                                  </div>
                                  {profile.balanceCheckResult?.message && (
                                    <div className="text-gray-600 dark:text-gray-400 mt-1">
                                      {profile.balanceCheckResult.message}
                                    </div>
                                  )}
                                </div>
                              </td>
                              <td className="px-3 py-2 border-b">
                                <div className="text-xs space-y-1">
                                  {/* Main Token Balance */}
                                  {profile.balanceCheckResult?.mainTokenResult && (
                                    <div>
                                      <span className="font-medium">Main:</span>
                                      <span className="ml-1 text-green-600 dark:text-green-400">
                                        {profile.balanceCheckResult.mainTokenResult.currentBalance}
                                      </span>
                                      <span className="text-gray-500 dark:text-gray-400 ml-1">
                                        (req: {profile.balanceCheckResult.mainTokenResult.minimumRequired})
                                      </span>
                                    </div>
                                  )}
                                  {/* Secondary Token Results */}
                                  {profile.balanceCheckResult?.secondaryTokenResult && (
                                    <div>
                                      <span className="font-medium">Secondary:</span>
                                      <span className="ml-1 text-green-600 dark:text-green-400">
                                        {profile.balanceCheckResult.secondaryTokenResult.currentBalance}
                                      </span>
                                      <span className="text-gray-500 dark:text-gray-400 ml-1">
                                        (req: {profile.balanceCheckResult.secondaryTokenResult.minimumRequired})
                                      </span>
                                    </div>
                                  )}
                                  {/* Secondary Min Holdings */}
                                  {profile.balanceCheckResult?.secondaryMinHoldingsResult && (
                                    <div>
                                      <span className="font-medium">Min Holdings:</span>
                                      <span className="ml-1 text-green-600 dark:text-green-400">
                                        {profile.balanceCheckResult.secondaryMinHoldingsResult.currentBalance}
                                      </span>
                                      <span className="text-gray-500 dark:text-gray-400 ml-1">
                                        (req: {profile.balanceCheckResult.secondaryMinHoldingsResult.minimumRequired})
                                      </span>
                                    </div>
                                  )}
                                </div>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          <div className="rounded-md border">
        <Table>
          <TableCaption>
            {selectedChainId && selectedChainId !== 'all'
              ? `Users on ${availableChains.find(c => c.id === selectedChainId)?.name || selectedChainId} (${users.length} total)`
              : `All users across all chains (${users.length} total)`
            }
          </TableCaption>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[200px]">Address</TableHead>
              <TableHead>Chain</TableHead>
              <TableHead>Profile Name</TableHead>
              <TableHead>Role</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Referral Code</TableHead>
              <TableHead>Referred By</TableHead>
              <TableHead>Expiry Date</TableHead>
              <TableHead>Transaction Hash</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {users && users.length > 0 ? users.map((user) => (
              <TableRow key={user.address}>
                <TableCell className="font-medium">
                  {user.address.substring(0, 6)}...{user.address.substring(user.address.length - 4)}
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-mono bg-neutral-100 dark:bg-neutral-800 px-2 py-1 rounded">
                      {availableChains.find(c => c.id === user.chain)?.name || user.chain}
                    </span>
                  </div>
                </TableCell>
                <TableCell>{user.profileName || user.name}</TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    {getRoleIcon(user.role)}
                    <span className="capitalize">{user.role}</span>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    {getStatusIcon(user.status)}
                    <span className="capitalize">{user.status}</span>
                  </div>
                </TableCell>
                <TableCell>
                  {user.referralCode ? (
                    <span className="font-mono text-sm bg-neutral-100 dark:bg-neutral-800 px-2 py-1 rounded">
                      {user.referralCode}
                    </span>
                  ) : (
                    <span className="text-neutral-400">N/A</span>
                  )}
                </TableCell>
                <TableCell>
                  {user.referredBy ? (
                    <span className="font-mono text-sm text-blue-600 dark:text-blue-400">
                      {user.referredBy}
                    </span>
                  ) : (
                    <span className="text-neutral-400">N/A</span>
                  )}
                </TableCell>
                <TableCell>{formatDate(user.expiryDate)}</TableCell>
                <TableCell>
                  {user.transactionHash ? (
                    <a
                      href={getCronoscanUrl(user.transactionHash)}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-1 text-blue-500 hover:underline"
                    >
                      {user.transactionHash.substring(0, 6)}...{user.transactionHash.substring(user.transactionHash.length - 4)}
                      <ExternalLink className="h-3 w-3" />
                    </a>
                  ) : (
                    'N/A'
                  )}
                </TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuSeparator />

                      {/* Role actions */}
                      <DropdownMenuLabel className="text-xs text-muted-foreground">Change Role</DropdownMenuLabel>
                      <DropdownMenuItem onClick={() => updateUserRole(user.address, 'admin')}>
                        <Shield className="mr-2 h-4 w-4" />
                        <span>Set as Admin</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => updateUserRole(user.address, 'user')}>
                        <User className="mr-2 h-4 w-4" />
                        <span>Set as User</span>
                      </DropdownMenuItem>

                      <DropdownMenuSeparator />

                      {/* Status actions */}
                      <DropdownMenuLabel className="text-xs text-muted-foreground">Change Status</DropdownMenuLabel>
                      <DropdownMenuItem onClick={() => updateUserStatus(user.address, 'new')}>
                        <Clock className="mr-2 h-4 w-4 text-blue-500" />
                        <span>Set as New</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => updateUserStatus(user.address, 'in-progress')}>
                        <Clock className="mr-2 h-4 w-4 text-yellow-500" />
                        <span>Set as In Progress</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => updateUserStatus(user.address, 'pending')}>
                        <AlertTriangle className="mr-2 h-4 w-4 text-orange-500" />
                        <span>Set as Pending</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => updateUserStatus(user.address, 'approved')}>
                        <Check className="mr-2 h-4 w-4 text-green-500" />
                        <span>Set as Approved</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => updateUserStatus(user.address, 'deleted')}>
                        <Trash2 className="mr-2 h-4 w-4 text-red-500" />
                        <span>Set as Deleted</span>
                      </DropdownMenuItem>

                      <DropdownMenuSeparator />

                      {/* Transaction hash action */}
                      <DropdownMenuItem
                        onClick={() => {
                          const hash = prompt('Enter transaction hash:');
                          if (hash) updateTransactionHash(user.address, hash);
                        }}
                      >
                        <ExternalLink className="mr-2 h-4 w-4" />
                        <span>Update Transaction Hash</span>
                      </DropdownMenuItem>

                      {/* Expiry date action */}
                      <DropdownMenuItem
                        onClick={() => {
                          const date = prompt('Enter expiry date (YYYY-MM-DD):');
                          if (date) {
                            const expiryDate = new Date(date);
                            if (!isNaN(expiryDate.getTime())) {
                              updateExpiryDate(user.address, expiryDate.toISOString());
                            } else {
                              toast.error('Invalid date format');
                            }
                          }
                        }}
                      >
                        <Clock className="mr-2 h-4 w-4" />
                        <span>Update Expiry Date</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            )) : (
              <TableRow>
                <TableCell colSpan={10} className="text-center py-4">
                  {selectedChainId
                    ? `No users found on ${availableChains.find(c => c.id === selectedChainId)?.name || selectedChainId}`
                    : 'No users found'
                  }
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
        </TabsContent>

        <TabsContent value="settings" className="mt-4">

          <div className="grid gap-6 md:grid-cols-2 mb-6">
            {/* Token Requirements Card */}
            <Card>
              <CardHeader>
                <CardTitle>Token Requirements</CardTitle>
                <CardDescription>
                  {selectedChainId
                    ? `Configure token requirements for ${availableChains.find(c => c.id === selectedChainId)?.name || selectedChainId}`
                    : 'Configure main and secondary token requirements'
                  }
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {/* Main Token Requirement */}
                  <div className="p-4 border rounded-lg space-y-4">
                    <h4 className="font-medium text-green-600">Main Token Requirement</h4>
                    <p className="text-sm text-muted-foreground">
                      Primary token requirements. "Minimum Holdings" for regular users, "Secondary Min Holdings" for users holding partner tokens
                    </p>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Chain ID</Label>
                        <Input
                          value={tokenRequirements.mainRequirement?.chainId || ''}
                          onChange={(e) => updateMainRequirement('chainId', e.target.value)}
                          placeholder="e.g., 25"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Chain Name</Label>
                        <Input
                          value={tokenRequirements.mainRequirement?.chainName || ''}
                          onChange={(e) => updateMainRequirement('chainName', e.target.value)}
                          placeholder="e.g., Cronos"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Token Address</Label>
                        <Input
                          value={tokenRequirements.mainRequirement?.tokenAddress || ''}
                          onChange={(e) => updateMainRequirement('tokenAddress', e.target.value)}
                          placeholder="0x..."
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Token Name</Label>
                        <Input
                          value={tokenRequirements.mainRequirement?.tokenName || ''}
                          onChange={(e) => updateMainRequirement('tokenName', e.target.value)}
                          placeholder="e.g., Web3Tools"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Minimum Holdings</Label>
                        <Input
                          value={tokenRequirements.mainRequirement?.minimumHoldings || ''}
                          onChange={(e) => updateMainRequirement('minimumHoldings', e.target.value)}
                          placeholder="e.g., 100"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Secondary Min Holdings</Label>
                        <Input
                          value={tokenRequirements.mainRequirement?.secondaryMinHoldings || ''}
                          onChange={(e) => updateMainRequirement('secondaryMinHoldings', e.target.value)}
                          placeholder="e.g., 50"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Secondary Requirements */}
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium text-blue-600">Secondary Token Requirements</h4>
                      <p className="text-sm text-muted-foreground">
                        Alternative tokens that allow users to hold the "Secondary Min Holdings" amount of the main token instead of the full amount
                      </p>
                    </div>

                    {tokenRequirements.secondaryRequirements.length === 0 && (
                      <p className="text-sm text-muted-foreground text-center py-4 border border-dashed rounded-lg">
                        No secondary token requirements configured. Click "Add Secondary Token Requirement" below to add one.
                      </p>
                    )}

                    {tokenRequirements.secondaryRequirements.map((secReq) => (
                      <div key={secReq.id} className="p-4 border rounded-lg space-y-4">
                        <div className="flex items-center justify-between">
                          <h5 className="font-medium">Secondary Token</h5>
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => removeSecondaryTokenRequirement(secReq.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>

                        {/* Token Details */}
                        <div className="grid grid-cols-3 gap-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                          <div className="space-y-2">
                            <Label className="text-blue-700 dark:text-blue-300">Token Address</Label>
                            <Input
                              value={secReq.tokenAddress}
                              onChange={(e) => updateSecondaryTokenRequirement(secReq.id, 'tokenAddress', e.target.value)}
                              placeholder="0x..."
                            />
                          </div>
                          <div className="space-y-2">
                            <Label className="text-blue-700 dark:text-blue-300">Token Name</Label>
                            <Input
                              value={secReq.tokenName}
                              onChange={(e) => updateSecondaryTokenRequirement(secReq.id, 'tokenName', e.target.value)}
                              placeholder="e.g., USDC"
                            />
                          </div>
                          <div className="space-y-2">
                            <Label className="text-blue-700 dark:text-blue-300">Minimum Holdings</Label>
                            <Input
                              value={secReq.minimumHoldings}
                              onChange={(e) => updateSecondaryTokenRequirement(secReq.id, 'minimumHoldings', e.target.value)}
                              placeholder="e.g., 1000"
                            />
                          </div>
                        </div>


                      </div>
                    ))}

                    {/* Add Secondary Token Requirement Button */}
                    <Button
                      variant="outline"
                      onClick={addSecondaryTokenRequirement}
                      className="w-full"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add Secondary Token Requirement
                    </Button>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button onClick={updateTokenRequirements}>Save Token Settings</Button>
              </CardFooter>
            </Card>

            {/* Profile Defaults Card */}
            <Card>
              <CardHeader>
                <CardTitle>Profile Defaults</CardTitle>
                <CardDescription>
                  {selectedChainId
                    ? `Configure default settings for new profiles on ${availableChains.find(c => c.id === selectedChainId)?.name || selectedChainId}`
                    : 'Configure default settings for new profiles'
                  }
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="default_role">Default Role</Label>
                  <Input
                    id="default_role"
                    type="text"
                    value={profileDefaults.default_role}
                    onChange={(e) => setProfileDefaults({
                      ...profileDefaults,
                      default_role: e.target.value
                    })}
                  />
                  <p className="text-sm text-muted-foreground">Default role for new users (user/admin)</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="default_status">Default Status</Label>
                  <Input
                    id="default_status"
                    type="text"
                    value={profileDefaults.default_status}
                    onChange={(e) => setProfileDefaults({
                      ...profileDefaults,
                      default_status: e.target.value
                    })}
                  />
                  <p className="text-sm text-muted-foreground">Default status for new users (new/in-progress/pending/approved/deleted)</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="default_expiry_days">Default Expiry Days</Label>
                  <Input
                    id="default_expiry_days"
                    type="text"
                    value={profileDefaults.default_expiry_days}
                    onChange={(e) => setProfileDefaults({
                      ...profileDefaults,
                      default_expiry_days: e.target.value
                    })}
                  />
                  <p className="text-sm text-muted-foreground">Default number of days until profile expiry</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="default_profile_name_format">Default Profile Name Format</Label>
                  <Input
                    id="default_profile_name_format"
                    type="text"
                    value={profileDefaults.default_profile_name_format}
                    onChange={(e) => setProfileDefaults({
                      ...profileDefaults,
                      default_profile_name_format: e.target.value
                    })}
                  />
                  <p className="text-sm text-muted-foreground">Default format for profile names</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="default_profile_bio">Default Profile Bio</Label>
                  <Input
                    id="default_profile_bio"
                    type="text"
                    value={profileDefaults.default_profile_bio}
                    onChange={(e) => setProfileDefaults({
                      ...profileDefaults,
                      default_profile_bio: e.target.value
                    })}
                  />
                  <p className="text-sm text-muted-foreground">Default bio text for new profiles</p>
                </div>
              </CardContent>
              <CardFooter>
                <Button onClick={updateProfileDefaults}>Save Profile Defaults</Button>
              </CardFooter>
            </Card>
          </div>

          {/* Featured Profile Card */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Featured Profile</CardTitle>
              <CardDescription>
                {selectedChainId
                  ? `Set the profile to feature on the discovery page for ${availableChains.find(c => c.id === selectedChainId)?.name || selectedChainId}`
                  : 'Set the profile to feature on the discovery page'
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="featured-profile">Featured Profile Name</Label>
                  <Input
                    id="featured-profile"
                    value={featuredProfile}
                    onChange={(e) => setFeaturedProfile(e.target.value)}
                    placeholder="e.g., web3tools"
                  />
                  <p className="text-sm text-muted-foreground">
                    Enter the name of the profile you want to feature on the discovery page for this chain.
                    Make sure this profile exists in the database and is on the selected chain.
                  </p>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={updateFeaturedProfile} disabled={settingsLoading}>
                {settingsLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  'Save Featured Profile'
                )}
              </Button>
            </CardFooter>
          </Card>

          {/* Component Defaults Card */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Component Defaults</CardTitle>
              <CardDescription>
                {selectedChainId
                  ? `Configure default components for new profiles on ${availableChains.find(c => c.id === selectedChainId)?.name || selectedChainId}`
                  : 'Configure default components for new profiles'
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {componentDefaults.defaults?.map((component, index) => (
                  <div key={index} className="p-3 border rounded-md space-y-4">
                    <div className="flex items-end gap-2">
                      <div className="flex-1 space-y-2">
                        <Label htmlFor={`component-type-${index}`}>Component Type</Label>
                        <Input
                          id={`component-type-${index}`}
                          value={component.componentType}
                          onChange={(e) => updateComponentDefault(index, 'componentType', e.target.value)}
                          placeholder="e.g., banner, profilePicture"
                        />
                      </div>
                      <div className="w-20 space-y-2">
                        <Label htmlFor={`order-${index}`}>Order</Label>
                        <Input
                          id={`order-${index}`}
                          value={component.order}
                          onChange={(e) => updateComponentDefault(index, 'order', e.target.value)}
                          placeholder="e.g., 1, 2"
                        />
                      </div>
                      <div className="w-24 space-y-2">
                        <Label htmlFor={`hidden-${index}`}>Hidden</Label>
                        <Input
                          id={`hidden-${index}`}
                          value={component.hidden}
                          onChange={(e) => updateComponentDefault(index, 'hidden', e.target.value)}
                          placeholder="Y/N"
                        />
                      </div>
                      <Button
                        variant="destructive"
                        size="icon"
                        onClick={() => removeComponentDefault(index)}
                        className="mb-0.5"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>

                    {/* Component Details Section */}
                    <div className="space-y-3 border-t pt-3">
                      <h4 className="text-sm font-medium">Component Details</h4>

                      {/* Common details for all components */}
                      <div className="grid grid-cols-2 gap-2">
                        <div className="space-y-1">
                          <Label htmlFor={`bg-color-${index}`} className="text-xs">Background Color</Label>
                          <Input
                            id={`bg-color-${index}`}
                            value={component.details?.backgroundColor || 'transparent'}
                            onChange={(e) => updateComponentDetails(index, 'backgroundColor', e.target.value)}
                            placeholder="transparent"
                            className="h-8 text-sm"
                          />
                        </div>
                        <div className="space-y-1">
                          <Label htmlFor={`font-color-${index}`} className="text-xs">Font Color</Label>
                          <Input
                            id={`font-color-${index}`}
                            value={component.details?.fontColor || ''}
                            onChange={(e) => updateComponentDetails(index, 'fontColor', e.target.value || null)}
                            placeholder="null"
                            className="h-8 text-sm"
                          />
                        </div>
                      </div>

                      {/* Note: Image positioning is now handled in the componentImages table */}

                      {/* Profile Picture specific details */}
                      {component.componentType === 'profilePicture' && (
                        <div className="grid grid-cols-2 gap-2">
                          <div className="space-y-1">
                            <Label htmlFor={`shape-${index}`} className="text-xs">Shape</Label>
                            <Input
                              id={`shape-${index}`}
                              value={component.details?.shape || 'circular'}
                              onChange={(e) => updateComponentDetails(index, 'shape', e.target.value)}
                              placeholder="circular"
                              className="h-8 text-sm"
                            />
                          </div>
                          <div className="space-y-1">
                            <Label htmlFor={`default-image-${index}`} className="text-xs">Default Image Path</Label>
                            <Input
                              id={`default-image-${index}`}
                              value={component.details?.defaultImagePath || 'pfp.jpg'}
                              onChange={(e) => updateComponentDetails(index, 'defaultImagePath', e.target.value)}
                              placeholder="pfp.jpg"
                              className="h-8 text-sm"
                            />
                          </div>
                        </div>
                      )}

                      {/* Banner specific details */}
                      {component.componentType === 'banner' && (
                        <div className="space-y-1">
                          <Label htmlFor={`default-banner-${index}`} className="text-xs">Default Image Path</Label>
                          <Input
                            id={`default-banner-${index}`}
                            value={component.details?.defaultImagePath || 'banner.png'}
                            onChange={(e) => updateComponentDetails(index, 'defaultImagePath', e.target.value)}
                            placeholder="banner.png"
                            className="h-8 text-sm"
                          />
                        </div>
                      )}
                    </div>
                  </div>
                ))}
                <Button
                  variant="outline"
                  onClick={addComponentDefault}
                  className="w-full"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Component
                </Button>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={updateComponentDefaults}>Save Component Defaults</Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="token-holders" className="mt-4">
          <TokenHolderTool selectedChainId={selectedChainId} />
        </TabsContent>

        <TabsContent value="database" className="mt-4">
          <div className="space-y-6">
            {/* SQL Query Executor */}
            <Card>
              <CardHeader>
                <CardTitle>SQL Query Executor</CardTitle>
                <CardDescription>Execute custom SQL queries (use with caution)</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="sql-query">SQL Query</Label>
                  <Textarea
                    id="sql-query"
                    value={sqlQuery}
                    onChange={(e) => setSqlQuery(e.target.value)}
                    placeholder="SELECT * FROM web3Profile LIMIT 10;"
                    rows={4}
                  />
                </div>
                <div className="flex gap-2">
                  <Button
                    onClick={executeSqlQuery}
                    disabled={isExecutingQuery}
                  >
                    {isExecutingQuery ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Executing...
                      </>
                    ) : (
                      <>
                        <Play className="mr-2 h-4 w-4" />
                        Execute Query
                      </>
                    )}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setSqlQuery('')}
                  >
                    Clear
                  </Button>
                </div>

                {/* SQL Results */}
                {sqlResult && (
                  <div className="space-y-4">
                    <div className="text-sm text-muted-foreground">
                      {sqlResult.error ? (
                        <div className="text-red-600 dark:text-red-400">
                          <strong>Error:</strong> {sqlResult.error}
                        </div>
                      ) : (
                        <>
                          {sqlResult.type === 'select'
                            ? `${sqlResult.rowCount} rows returned`
                            : 'Query executed successfully'
                          } • {sqlResult.executionTime}
                        </>
                      )}
                    </div>

                    {!sqlResult.error && sqlResult.rows && sqlResult.rows.length > 0 && (
                      <div className="rounded-md border bg-gray-50 dark:bg-gray-900">
                        <ScrollArea className="h-96">
                          <pre className="p-4 text-sm overflow-auto">
                            {JSON.stringify(sqlResult.rows, null, 2)}
                          </pre>
                        </ScrollArea>
                      </div>
                    )}

                    {!sqlResult.error && sqlResult.rows && sqlResult.rows.length === 0 && (
                      <div className="text-center py-8 text-muted-foreground">
                        No rows returned
                      </div>
                    )}

                    {!sqlResult.error && sqlResult.type !== 'select' && (
                      <div className="rounded-md border bg-gray-50 dark:bg-gray-900">
                        <pre className="p-4 text-sm overflow-auto">
                          {JSON.stringify(sqlResult.result, null, 2)}
                        </pre>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Profile Address Transfer */}
            <Card>
              <CardHeader>
                <CardTitle>Profile Address Transfer</CardTitle>
                <CardDescription>Transfer all profile data from one wallet address to another</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <p className="text-sm text-muted-foreground">
                    This will update ALL database records to change the wallet address. Use this to transfer a complete profile you created with your wallet to another user's wallet address.
                  </p>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="from-address">From Address (Your Wallet)</Label>
                      <Input
                        id="from-address"
                        placeholder="0x... (source address)"
                        value={fromAddress}
                        onChange={(e) => setFromAddress(e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="to-address">To Address (Target User)</Label>
                      <Input
                        id="to-address"
                        placeholder="0x... (destination address)"
                        value={toAddress}
                        onChange={(e) => setToAddress(e.target.value)}
                      />
                    </div>
                  </div>
                  <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                    <div className="flex items-start gap-2">
                      <AlertTriangle className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5" />
                      <div>
                        <h4 className="font-medium text-yellow-800 dark:text-yellow-200">Warning</h4>
                        <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
                          This operation will update records in ALL tables: web3Profile, componentPositions, componentImages, and any other tables that reference the address. If the destination address already has a profile, it will be completely deleted and replaced.
                        </p>
                      </div>
                    </div>
                  </div>
                  <Button
                    onClick={transferProfile}
                    disabled={!fromAddress || !toAddress}
                    className="w-full"
                  >
                    Transfer Profile
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>


    </div>
  );
}
