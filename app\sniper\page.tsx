'use client';

import { useState, useEffect, useRef } from 'react';
import { useAppKitAccount, useAppKitNetwork } from '@reown/appkit/react';
import { useRouter } from 'next/navigation';
import { Loader2, Play, Square, Eye, ExternalLink, RefreshCw, Coins, AlertCircle, Settings, Key, DollarSign, Zap, Plus, Trash2, Users, Target } from 'lucide-react';
import { toast } from 'sonner';

interface User {
  address: string;
  role: string;
  status: string;
  name?: string;
}

interface Transaction {
  hash: string;
  from: string;
  to: string;
  value: string;
  timestamp: number;
  blockNumber: number;
  isTokenCreation?: boolean;
  createdTokenAddress?: string;
  tokenName?: string;
  tokenSymbol?: string;
}

interface SniperConfig {
  privateKey: string;
  buyAmount: string;
  slippage: string;
  autoBuyEnabled: boolean;
}

interface Creator {
  id: number;
  address: string;
  name: string;
  chainId: string;
  isActive: string;
}

interface WatchedAddress {
  id: number;
  address: string;
  name?: string;
  chainId: string;
  isActive: string;
}

export default function SniperPage() {
  const { isConnected, address } = useAppKitAccount();
  const { chainId } = useAppKitNetwork();
  const router = useRouter();

  const [loading, setLoading] = useState(true);
  const [isAdmin, setIsAdmin] = useState(false);
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [lastChecked, setLastChecked] = useState<Date | null>(null);
  const [fetchingTransactions, setFetchingTransactions] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Sniper configuration state
  const [showConfig, setShowConfig] = useState(false);
  const [config, setConfig] = useState<SniperConfig>({
    privateKey: '',
    buyAmount: '10',
    slippage: '5',
    autoBuyEnabled: false
  });

  // Creator and watched address management
  const [creators, setCreators] = useState<Creator[]>([]);
  const [watchedAddresses, setWatchedAddresses] = useState<WatchedAddress[]>([]);
  const [selectedCreators, setSelectedCreators] = useState<number[]>([]);
  const [selectedWatchedAddresses, setSelectedWatchedAddresses] = useState<number[]>([]);
  const [showCreatorForm, setShowCreatorForm] = useState(false);
  const [showWatchedForm, setShowWatchedForm] = useState(false);
  const [newCreator, setNewCreator] = useState({ address: '', name: '' });
  const [newWatchedAddress, setNewWatchedAddress] = useState({ address: '', name: '' });

  // Load creators and watched addresses
  const loadCreators = async () => {
    if (!chainId) return;

    try {
      const response = await fetch(`/api/sniper/creators?chainId=${chainId}`);
      if (response.ok) {
        const data = await response.json();
        setCreators(data.creators || []);
      }
    } catch (error) {
      console.error('Error loading creators:', error);
    }
  };

  const loadWatchedAddresses = async () => {
    if (!chainId) return;

    try {
      const response = await fetch(`/api/sniper/watched-addresses?chainId=${chainId}`);
      if (response.ok) {
        const data = await response.json();
        setWatchedAddresses(data.watchedAddresses || []);
      }
    } catch (error) {
      console.error('Error loading watched addresses:', error);
    }
  };

  const loadConfig = async () => {
    if (!address || !chainId) return;

    try {
      const response = await fetch(`/api/sniper/config?userAddress=${address}&chainId=${chainId}`);
      if (response.ok) {
        const data = await response.json();
        if (data.config) {
          setConfig({
            privateKey: data.config.privateKey || '',
            buyAmount: data.config.buyAmount || '10',
            slippage: data.config.slippage || '5',
            autoBuyEnabled: data.config.autoBuyEnabled === 'Y'
          });
        }
      }
    } catch (error) {
      console.error('Error loading config:', error);
    }
  };

  // Check admin status and load data
  useEffect(() => {
    const checkAdminStatus = async () => {
      if (!isConnected || !address || !chainId) {
        setLoading(false);
        setIsAdmin(false);
        return;
      }

      try {
        console.log('Checking admin status for:', address);
        const response = await fetch(`/api/admin/users/chain/${chainId}`);

        if (!response.ok) {
          throw new Error('Failed to fetch users');
        }

        const data: User[] = await response.json();
        const currentUser = data.find((user: User) => user.address.toLowerCase() === address.toLowerCase());

        if (currentUser && currentUser.role === 'admin') {
          console.log('User is admin, granting access');
          setIsAdmin(true);
          // Load data after confirming admin status
          await Promise.all([loadCreators(), loadWatchedAddresses(), loadConfig()]);
        } else {
          console.log('User is not admin or not found');
          setIsAdmin(false);
        }
      } catch (error) {
        console.error('Error checking admin status:', error);
        toast.error('Failed to verify admin status');
        setIsAdmin(false);
      } finally {
        setLoading(false);
      }
    };

    checkAdminStatus();
  }, [isConnected, address, chainId]);

  // Add creator
  const addCreator = async () => {
    if (!newCreator.address || !newCreator.name) {
      toast.error('Please enter both address and name');
      return;
    }

    if (!newCreator.address.match(/^0x[a-fA-F0-9]{40}$/)) {
      toast.error('Please enter a valid address');
      return;
    }

    try {
      const response = await fetch('/api/sniper/creators', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          address: newCreator.address,
          name: newCreator.name,
          chainId: chainId?.toString()
        })
      });

      const data = await response.json();
      if (data.success) {
        toast.success('Creator added successfully');
        setNewCreator({ address: '', name: '' });
        setShowCreatorForm(false);
        await loadCreators();
      } else {
        toast.error(data.error || 'Failed to add creator');
      }
    } catch (error) {
      console.error('Error adding creator:', error);
      toast.error('Failed to add creator');
    }
  };

  // Add watched address
  const addWatchedAddress = async () => {
    if (!newWatchedAddress.address) {
      toast.error('Please enter an address');
      return;
    }

    if (!newWatchedAddress.address.match(/^0x[a-fA-F0-9]{40}$/)) {
      toast.error('Please enter a valid address');
      return;
    }

    try {
      const response = await fetch('/api/sniper/watched-addresses', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          address: newWatchedAddress.address,
          name: newWatchedAddress.name || null,
          chainId: chainId?.toString()
        })
      });

      const data = await response.json();
      if (data.success) {
        toast.success('Watched address added successfully');
        setNewWatchedAddress({ address: '', name: '' });
        setShowWatchedForm(false);
        await loadWatchedAddresses();
      } else {
        toast.error(data.error || 'Failed to add watched address');
      }
    } catch (error) {
      console.error('Error adding watched address:', error);
      toast.error('Failed to add watched address');
    }
  };

  // Save configuration
  const saveConfig = async () => {
    if (!address || !chainId) return;

    try {
      const response = await fetch('/api/sniper/config', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userAddress: address,
          chainId: chainId.toString(),
          privateKey: config.privateKey,
          buyAmount: config.buyAmount,
          slippage: config.slippage,
          autoBuyEnabled: config.autoBuyEnabled
        })
      });

      const data = await response.json();
      if (data.success) {
        toast.success('Configuration saved successfully');
      } else {
        toast.error(data.error || 'Failed to save configuration');
      }
    } catch (error) {
      console.error('Error saving config:', error);
      toast.error('Failed to save configuration');
    }
  };

  // Fetch transactions for multiple addresses
  const fetchTransactions = async (showLoading = true) => {
    try {
      if (showLoading) setFetchingTransactions(true);

      // Determine which addresses to monitor
      let addressesToMonitor: string[] = [];

      if (selectedWatchedAddresses.length > 0) {
        // If watched addresses are selected, only monitor those
        addressesToMonitor = watchedAddresses
          .filter(wa => selectedWatchedAddresses.includes(wa.id))
          .map(wa => wa.address);
      } else if (selectedCreators.length > 0) {
        // If no watched addresses but creators selected, monitor all creator addresses
        addressesToMonitor = creators
          .filter(c => selectedCreators.includes(c.id))
          .map(c => c.address);
      }

      if (addressesToMonitor.length === 0) {
        toast.error('Please select creators or watched addresses to monitor');
        return;
      }

      // Fetch transactions for all selected addresses
      const allTransactions: Transaction[] = [];

      for (const address of addressesToMonitor) {
        const response = await fetch(`/api/sniper/transactions?address=${address}&chainId=${chainId}&limit=5`);

        if (response.ok) {
          const data = await response.json();
          const transactions = data.transactions || [];
          allTransactions.push(...transactions);
        }
      }

      // Remove duplicates and sort by block number
      const uniqueTransactions = allTransactions.filter((tx, index, self) =>
        index === self.findIndex(t => t.hash === tx.hash)
      );
      uniqueTransactions.sort((a, b) => b.blockNumber - a.blockNumber);

      // Check for token creation transactions
      const tokenCreations = uniqueTransactions.filter((tx: Transaction) => tx.isTokenCreation);

      setTransactions(uniqueTransactions.slice(0, 20)); // Limit to 20 most recent
      setLastChecked(new Date());

      if (tokenCreations.length > 0) {
        tokenCreations.forEach((tx: Transaction) => {
          toast.success(
            `🚀 NEW TOKEN CREATED! Address: ${tx.createdTokenAddress}`,
            { duration: 10000 }
          );

          // Auto-buy if enabled and configured
          if (config.autoBuyEnabled && config.privateKey && tx.createdTokenAddress) {
            handleAutoBuy(tx.createdTokenAddress);
          }
        });
      } else if (uniqueTransactions.length > 0) {
        toast.success(`Found ${uniqueTransactions.length} recent transactions`);
      }
    } catch (error) {
      console.error('Error fetching transactions:', error);
      toast.error('Failed to fetch transactions');
    } finally {
      if (showLoading) setFetchingTransactions(false);
    }
  };

  // Start monitoring
  const startMonitoring = async () => {
    if (selectedCreators.length === 0 && selectedWatchedAddresses.length === 0) {
      toast.error('Please select at least one creator or watched address to monitor');
      return;
    }

    setIsMonitoring(true);

    const monitoringTargets = [];
    if (selectedWatchedAddresses.length > 0) {
      monitoringTargets.push(`${selectedWatchedAddresses.length} watched addresses`);
    }
    if (selectedCreators.length > 0) {
      monitoringTargets.push(`${selectedCreators.length} creators`);
    }

    toast.success(`Started monitoring: ${monitoringTargets.join(' and ')}`);

    // Initial fetch
    await fetchTransactions();

    // Set up polling every 10 seconds (don't show loading for background fetches)
    intervalRef.current = setInterval(() => {
      fetchTransactions(false);
    }, 10000);
  };

  // Stop monitoring
  const stopMonitoring = () => {
    setIsMonitoring(false);
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    toast.info('Stopped monitoring');
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  // Format address for display
  const formatAddress = (addr: string) => {
    return `${addr.slice(0, 6)}...${addr.slice(-4)}`;
  };

  // Format timestamp
  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  // Format value (assuming it's in wei)
  const formatValue = (value: string) => {
    const ethValue = parseFloat(value) / Math.pow(10, 18);
    return ethValue.toFixed(6);
  };

  // Get explorer URL based on chain
  const getExplorerUrl = (hash: string) => {
    const chainExplorers: { [key: string]: string } = {
      '25': 'https://cronoscan.com/tx/',
      '1': 'https://etherscan.io/tx/',
      '137': 'https://polygonscan.com/tx/',
      '56': 'https://bscscan.com/tx/',
      '42161': 'https://arbiscan.io/tx/',
    };

    const baseUrl = chainExplorers[chainId?.toString() || '25'] || 'https://cronoscan.com/tx/';
    return `${baseUrl}${hash}`;
  };

  // Handle auto-buying a newly created token
  const handleAutoBuy = async (tokenAddress: string) => {
    if (!config.privateKey) {
      toast.error('Private key not configured for auto-buy');
      return;
    }

    try {
      toast.info(`🤖 AUTO-BUYING token ${formatAddress(tokenAddress)} with ${config.buyAmount} native tokens...`);

      const response = await fetch('/api/sniper/auto-buy', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tokenAddress,
          amount: config.buyAmount,
          slippage: config.slippage,
          privateKey: config.privateKey,
          chainId: chainId?.toString() || '25'
        })
      });

      const data = await response.json();

      if (data.success) {
        toast.success(`🎉 AUTO-BUY SUCCESSFUL! TX: ${data.transactionHash}`);
        console.log('Auto-buy details:', data);
      } else {
        toast.error(`❌ Auto-buy failed: ${data.error}`);
        console.error('Auto-buy error details:', data);
      }
    } catch (error) {
      console.error('Error in auto-buy:', error);
      toast.error('Auto-buy failed due to network error');
    }
  };

  // Test auto-buy configuration
  const testAutoBuyConfig = async () => {
    if (!config.privateKey) {
      toast.error('Please configure private key first');
      return;
    }

    try {
      toast.info('🧪 Testing auto-buy configuration...');

      // Test with a well-known token address (USDC on Cronos for example)
      const testTokenAddress = '******************************************'; // USDC on Cronos

      const response = await fetch('/api/sniper/auto-buy', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tokenAddress: testTokenAddress,
          amount: '0.1', // Small test amount
          slippage: config.slippage,
          privateKey: config.privateKey,
          chainId: chainId?.toString() || '25'
        })
      });

      const data = await response.json();

      if (data.success) {
        toast.success(`✅ Configuration test successful! TX: ${data.transactionHash}`);
      } else {
        toast.warning(`⚠️ Configuration test failed: ${data.error}`);
        console.log('Test details:', data);
      }
    } catch (error) {
      console.error('Error testing configuration:', error);
      toast.error('Configuration test failed');
    }
  };



  // Show loading state
  if (loading) {
    return (
      <div className="container mx-auto p-4">
        <h1 className="text-2xl font-bold mb-4">Sniper Dashboard</h1>
        <div className="flex items-center gap-2">
          <Loader2 className="h-4 w-4 animate-spin" />
          <p>Loading sniper dashboard...</p>
        </div>
        <div className="mt-4 text-sm text-gray-600">
          <p>Connected: {String(isConnected)}</p>
          <p>Address: {address || 'None'}</p>
          <p>Network: {chainId ? `Chain ${chainId}` : 'Not connected'}</p>
        </div>
      </div>
    );
  }

  // Show connection required message
  if (!isConnected || !address || !chainId) {
    return (
      <div className="container mx-auto p-4">
        <h1 className="text-2xl font-bold mb-4">Sniper Dashboard</h1>
        <p>Please connect your wallet and ensure you're on a supported network to access the sniper page.</p>
        <div className="mt-4 text-sm text-gray-600">
          <p>Connected: {String(isConnected)}</p>
          <p>Address: {address ? 'Yes' : 'No'}</p>
          <p>Network: {chainId ? `Chain ${chainId}` : 'Not connected'}</p>
        </div>
      </div>
    );
  }

  // Show access denied message
  if (!isAdmin) {
    return (
      <div className="container mx-auto p-4">
        <h1 className="text-2xl font-bold mb-4">Sniper Dashboard</h1>
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <p className="text-red-800 dark:text-red-200">You do not have permission to access this page.</p>
          <p className="text-sm text-red-600 dark:text-red-400 mt-2">
            Connected as: {address}
          </p>
          <p className="text-sm text-red-600 dark:text-red-400">
            Only admin users can access the sniper dashboard.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">Sniper Dashboard</h1>
        <button
          onClick={() => setShowConfig(!showConfig)}
          className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md
                     flex items-center gap-2 transition-colors"
        >
          <Settings className="h-4 w-4" />
          {showConfig ? 'Hide Config' : 'Show Config'}
        </button>
      </div>

      {/* Creator Management Section */}
      <div className="bg-gray-50 dark:bg-gray-900/50 border border-gray-200 dark:border-gray-800 rounded-lg p-6 mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold flex items-center gap-2">
            <Users className="h-5 w-5" />
            Token Creators ({creators.length})
          </h2>
          <button
            onClick={() => setShowCreatorForm(!showCreatorForm)}
            className="px-3 py-1.5 text-sm bg-green-600 hover:bg-green-700 text-white rounded-md
                       flex items-center gap-2 transition-colors"
          >
            <Plus className="h-4 w-4" />
            Add Creator
          </button>
        </div>

        {showCreatorForm && (
          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 mb-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Creator Address</label>
                <input
                  type="text"
                  value={newCreator.address}
                  onChange={(e) => setNewCreator(prev => ({ ...prev, address: e.target.value }))}
                  placeholder="0x..."
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md
                             bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100
                             focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Creator Name</label>
                <input
                  type="text"
                  value={newCreator.name}
                  onChange={(e) => setNewCreator(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Creator name"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md
                             bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100
                             focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
            <div className="flex gap-2 mt-4">
              <button
                onClick={addCreator}
                className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md"
              >
                Add Creator
              </button>
              <button
                onClick={() => {
                  setShowCreatorForm(false);
                  setNewCreator({ address: '', name: '' });
                }}
                className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-md"
              >
                Cancel
              </button>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
          {creators.map((creator) => (
            <div
              key={creator.id}
              className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                selectedCreators.includes(creator.id)
                  ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-300 dark:border-blue-700'
                  : 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700'
              }`}
              onClick={() => {
                setSelectedCreators(prev =>
                  prev.includes(creator.id)
                    ? prev.filter(id => id !== creator.id)
                    : [...prev, creator.id]
                );
              }}
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">{creator.name}</p>
                  <p className="text-sm text-gray-500 font-mono">{formatAddress(creator.address)}</p>
                </div>
                <input
                  type="checkbox"
                  checked={selectedCreators.includes(creator.id)}
                  onChange={() => {}}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
              </div>
            </div>
          ))}
        </div>

        {creators.length === 0 && (
          <p className="text-gray-500 text-center py-4">No creators added yet. Add creators to monitor their token creation activities.</p>
        )}
      </div>

      {/* Watched Addresses Section */}
      <div className="bg-gray-50 dark:bg-gray-900/50 border border-gray-200 dark:border-gray-800 rounded-lg p-6 mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold flex items-center gap-2">
            <Target className="h-5 w-5" />
            Watched Addresses ({watchedAddresses.length}) - Optional
          </h2>
          <button
            onClick={() => setShowWatchedForm(!showWatchedForm)}
            className="px-3 py-1.5 text-sm bg-purple-600 hover:bg-purple-700 text-white rounded-md
                       flex items-center gap-2 transition-colors"
          >
            <Plus className="h-4 w-4" />
            Add Address
          </button>
        </div>

        <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md">
          <p className="text-sm text-blue-800 dark:text-blue-200">
            <strong>Optional:</strong> If no watched addresses are selected, all transactions from selected creators will be monitored.
            If watched addresses are selected, only those specific addresses will be monitored.
          </p>
        </div>

        {showWatchedForm && (
          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 mb-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Address</label>
                <input
                  type="text"
                  value={newWatchedAddress.address}
                  onChange={(e) => setNewWatchedAddress(prev => ({ ...prev, address: e.target.value }))}
                  placeholder="0x..."
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md
                             bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100
                             focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Name (Optional)</label>
                <input
                  type="text"
                  value={newWatchedAddress.name}
                  onChange={(e) => setNewWatchedAddress(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Address name"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md
                             bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100
                             focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
            <div className="flex gap-2 mt-4">
              <button
                onClick={addWatchedAddress}
                className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-md"
              >
                Add Address
              </button>
              <button
                onClick={() => {
                  setShowWatchedForm(false);
                  setNewWatchedAddress({ address: '', name: '' });
                }}
                className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-md"
              >
                Cancel
              </button>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
          {watchedAddresses.map((watchedAddr) => (
            <div
              key={watchedAddr.id}
              className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                selectedWatchedAddresses.includes(watchedAddr.id)
                  ? 'bg-purple-50 dark:bg-purple-900/20 border-purple-300 dark:border-purple-700'
                  : 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700'
              }`}
              onClick={() => {
                setSelectedWatchedAddresses(prev =>
                  prev.includes(watchedAddr.id)
                    ? prev.filter(id => id !== watchedAddr.id)
                    : [...prev, watchedAddr.id]
                );
              }}
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">{watchedAddr.name || 'Unnamed Address'}</p>
                  <p className="text-sm text-gray-500 font-mono">{formatAddress(watchedAddr.address)}</p>
                </div>
                <input
                  type="checkbox"
                  checked={selectedWatchedAddresses.includes(watchedAddr.id)}
                  onChange={() => {}}
                  className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                />
              </div>
            </div>
          ))}
        </div>

        {watchedAddresses.length === 0 && (
          <p className="text-gray-500 text-center py-4">No watched addresses added. This is optional - leave empty to monitor all creator transactions.</p>
        )}
      </div>

      {/* Configuration Section */}
      {showConfig && (
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-6 mb-6">
          <h2 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Auto-Sniper Configuration
          </h2>

          <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md">
            <p className="text-sm text-blue-800 dark:text-blue-200">
              <strong>How it works:</strong> When monitoring detects a new token creation, the sniper will automatically execute a buy transaction using your configured private key and parameters.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="privateKey" className="block text-sm font-medium mb-2 flex items-center gap-2">
                <Key className="h-4 w-4" />
                Private Key (for auto-buy)
              </label>
              <input
                id="privateKey"
                type="password"
                value={config.privateKey}
                onChange={(e) => setConfig(prev => ({ ...prev, privateKey: e.target.value }))}
                placeholder="0x..."
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md
                           bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100
                           focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
              <p className="text-xs text-gray-500 mt-1">⚠️ Keep this secure! Used for automatic transactions.</p>
            </div>

            <div>
              <label htmlFor="buyAmount" className="block text-sm font-medium mb-2 flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                Buy Amount (Native Token)
              </label>
              <input
                id="buyAmount"
                type="number"
                value={config.buyAmount}
                onChange={(e) => setConfig(prev => ({ ...prev, buyAmount: e.target.value }))}
                placeholder="10"
                min="0.1"
                step="0.1"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md
                           bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100
                           focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label htmlFor="slippage" className="block text-sm font-medium mb-2">
                Slippage Tolerance (%)
              </label>
              <input
                id="slippage"
                type="number"
                value={config.slippage}
                onChange={(e) => setConfig(prev => ({ ...prev, slippage: e.target.value }))}
                placeholder="5"
                min="0.1"
                max="50"
                step="0.1"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md
                           bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100
                           focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <input
                  id="autoBuyEnabled"
                  type="checkbox"
                  checked={config.autoBuyEnabled}
                  onChange={(e) => setConfig(prev => ({ ...prev, autoBuyEnabled: e.target.checked }))}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="autoBuyEnabled" className="text-sm font-medium flex items-center gap-2">
                  <Zap className="h-4 w-4" />
                  Enable Auto-Buy
                </label>
              </div>

              <div className="flex gap-2">
                {config.privateKey && (
                  <button
                    onClick={testAutoBuyConfig}
                    className="px-3 py-1.5 text-sm bg-purple-600 hover:bg-purple-700 text-white rounded-md
                               flex items-center gap-2 transition-colors"
                  >
                    🧪 Test Config
                  </button>
                )}
                <button
                  onClick={saveConfig}
                  className="px-3 py-1.5 text-sm bg-green-600 hover:bg-green-700 text-white rounded-md
                             flex items-center gap-2 transition-colors"
                >
                  💾 Save Config
                </button>
              </div>
            </div>
          </div>

          <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md">
            <p className="text-sm text-blue-800 dark:text-blue-200">
              <strong>Auto-Buy Status:</strong> {config.autoBuyEnabled && config.privateKey ?
                '🟢 Ready - Will automatically buy new tokens' :
                '🔴 Disabled - Configure private key and enable to activate'
              }
            </p>
          </div>

          <div className="mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
            <h3 className="text-sm font-semibold text-red-800 dark:text-red-200 mb-2">⚠️ SECURITY WARNING</h3>
            <ul className="text-xs text-red-700 dark:text-red-300 space-y-1">
              <li>• Private keys are stored in browser memory only (not saved permanently)</li>
              <li>• Never share your private key with anyone</li>
              <li>• Use a dedicated wallet with limited funds for sniping</li>
              <li>• Auto-buy executes immediately when new tokens are detected</li>
              <li>• High slippage may result in significant losses</li>
              <li>• New tokens may be scams, rugs, or have no liquidity</li>
              <li>• Only invest what you can afford to lose completely</li>
            </ul>
          </div>
        </div>
      )}

      {/* Monitoring Control Section */}
      <div className="bg-gray-50 dark:bg-gray-900/50 border border-gray-200 dark:border-gray-800 rounded-lg p-6 mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold">Token Creation Monitor</h2>

          <div className="flex gap-2">
            {!isMonitoring ? (
              <button
                onClick={startMonitoring}
                disabled={selectedCreators.length === 0 && selectedWatchedAddresses.length === 0}
                className="px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white rounded-md
                           flex items-center gap-2 transition-colors disabled:cursor-not-allowed"
              >
                <Play className="h-4 w-4" />
                Start Monitoring
              </button>
            ) : (
              <button
                onClick={stopMonitoring}
                className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md
                           flex items-center gap-2 transition-colors"
              >
                <Square className="h-4 w-4" />
                Stop Monitoring
              </button>
            )}

            {(selectedCreators.length > 0 || selectedWatchedAddresses.length > 0) && (
              <button
                onClick={() => fetchTransactions()}
                disabled={fetchingTransactions}
                className="px-3 py-2 text-sm bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400
                           text-white rounded-md flex items-center gap-2 transition-colors"
              >
                <RefreshCw className={`h-4 w-4 ${fetchingTransactions ? 'animate-spin' : ''}`} />
                {fetchingTransactions ? 'Refreshing...' : 'Refresh'}
              </button>
            )}
          </div>
        </div>

        {/* Monitoring Status */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm text-gray-500">Selected Creators</p>
                <p className="text-lg font-semibold">{selectedCreators.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <div className="flex items-center gap-2">
              <Target className="h-5 w-5 text-purple-500" />
              <div>
                <p className="text-sm text-gray-500">Watched Addresses</p>
                <p className="text-lg font-semibold">{selectedWatchedAddresses.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <div className="flex items-center gap-2">
              <div className={`h-3 w-3 rounded-full ${isMonitoring ? 'bg-green-500 animate-pulse' : 'bg-gray-400'}`}></div>
              <div>
                <p className="text-sm text-gray-500">Status</p>
                <p className="text-lg font-semibold">{isMonitoring ? 'Monitoring' : 'Stopped'}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Last Checked */}
        {lastChecked && (
          <div className="text-sm text-gray-500">
            Last checked: {lastChecked.toLocaleTimeString()}
          </div>
        )}

        {/* Instructions */}
        {selectedCreators.length === 0 && selectedWatchedAddresses.length === 0 && (
          <div className="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md">
            <p className="text-sm text-yellow-800 dark:text-yellow-200">
              <strong>Getting Started:</strong> Select at least one creator or watched address above to start monitoring for token creation transactions.
            </p>
          </div>
        )}
      </div>

      {/* Transactions Table */}
      <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-800 flex items-center justify-between">
          <h2 className="text-lg font-semibold flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Recent Transactions ({transactions.length})
          </h2>

          {(selectedCreators.length > 0 || selectedWatchedAddresses.length > 0) && (
            <button
              onClick={() => fetchTransactions()}
              disabled={fetchingTransactions}
              className="px-3 py-1.5 text-sm bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400
                         text-white rounded-md flex items-center gap-2 transition-colors"
            >
              <RefreshCw className={`h-4 w-4 ${fetchingTransactions ? 'animate-spin' : ''}`} />
              {fetchingTransactions ? 'Refreshing...' : 'Refresh'}
            </button>
          )}
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Hash
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  From
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  To
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Value (Native)
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Time
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Block
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
              {transactions.length === 0 ? (
                <tr>
                  <td colSpan={7} className="px-6 py-8 text-center text-gray-500 dark:text-gray-400">
                    {isMonitoring ? 'No transactions found yet...' : 'Select creators/addresses and start monitoring to see transactions'}
                  </td>
                </tr>
              ) : (
                transactions.map((tx) => (
                  <tr key={tx.hash} className={`hover:bg-gray-50 dark:hover:bg-gray-800 ${tx.isTokenCreation ? 'bg-green-50 dark:bg-green-900/20 border-l-4 border-green-500' : ''}`}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center gap-2">
                        {tx.isTokenCreation ? (
                          <div className="flex items-center gap-1 text-green-600 dark:text-green-400">
                            <Coins className="h-4 w-4" />
                            <span className="text-xs font-semibold">TOKEN</span>
                          </div>
                        ) : (
                          <div className="flex items-center gap-1 text-gray-500">
                            <AlertCircle className="h-4 w-4" />
                            <span className="text-xs">TX</span>
                          </div>
                        )}
                      </div>
                      {tx.isTokenCreation && tx.createdTokenAddress && (
                        <div className="mt-1">
                          <div className="text-xs text-green-600 dark:text-green-400 font-mono">
                            {formatAddress(tx.createdTokenAddress)}
                          </div>
                          <div className="flex gap-2">
                            <button
                              onClick={() => {
                                navigator.clipboard.writeText(tx.createdTokenAddress!);
                                toast.success('Token address copied!');
                              }}
                              className="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 underline"
                            >
                              Copy Address
                            </button>
                            {config.autoBuyEnabled && config.privateKey ? (
                              <span className="text-xs text-green-600 dark:text-green-400 font-semibold">
                                ✅ Auto-bought
                              </span>
                            ) : (
                              <button
                                onClick={() => handleAutoBuy(tx.createdTokenAddress!)}
                                className="text-xs bg-purple-600 hover:bg-purple-700 text-white px-2 py-1 rounded flex items-center gap-1"
                                disabled={!config.privateKey}
                              >
                                <Zap className="h-3 w-3" />
                                {config.privateKey ? 'Buy Now' : 'Configure Key'}
                              </button>
                            )}
                          </div>
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center gap-2">
                        <span className="font-mono text-sm">{formatAddress(tx.hash)}</span>
                        <a
                          href={getExplorerUrl(tx.hash)}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                        >
                          <ExternalLink className="h-3 w-3" />
                        </a>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap font-mono text-sm">
                      {formatAddress(tx.from)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap font-mono text-sm">
                      {formatAddress(tx.to)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      {formatValue(tx.value)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {formatTimestamp(tx.timestamp)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {tx.blockNumber}
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
