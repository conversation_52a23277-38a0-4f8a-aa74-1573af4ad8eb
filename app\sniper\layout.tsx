import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "../globals.css";
import { ThemeProvider } from "@/components/ThemeProvider";
import { Toaster } from "sonner";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Sniper Dashboard - Web3Socials",
  description: "Token creation monitoring and auto-buy dashboard for Web3Socials",
};

export default function SniperLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <div className="dark">
      <ThemeProvider attribute="class" defaultTheme="dark" enableSystem={false} forcedTheme="dark">
        <main className="min-h-screen bg-background dark">
          {children}
        </main>
        <Toaster position="top-right" theme="dark" />
      </ThemeProvider>
    </div>
  );
}
